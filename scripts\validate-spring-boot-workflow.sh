#!/bin/bash

# Validation script for Spring Boot GitOps workflow with managed database
# This script validates the workflow configuration and tests the payload structure

set -e

echo "🧪 Validating Spring Boot GitOps Workflow with Managed Database"
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local message=$1
    local status=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        *)
            echo "$message"
            ;;
    esac
}

# Validate required files exist
print_status "Checking required files..." "INFO"

required_files=(
    "examples/spring-boot-cicd-workflow.yml"
    "docs/spring-boot-managed-database-integration.md"
    "docs/managed-database-payloads.md"
    "scripts/generate-manifests-cicd.py"
    ".github/workflows/deploy-from-cicd.yaml"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_status "Found: $file" "SUCCESS"
    else
        print_status "Missing: $file" "ERROR"
        exit 1
    fi
done

# Validate workflow template structure
print_status "Validating workflow template structure..." "INFO"

workflow_file="examples/spring-boot-cicd-workflow.yml"

# Check for required sections
required_sections=(
    "Create Managed Database Secrets Payload"
    "Trigger GitOps Deployment"
    "secrets-encoded"
    "springboot-backend"
    "ai-spring-backend-saipriya"
)

for section in "${required_sections[@]}"; do
    if grep -q "$section" "$workflow_file"; then
        print_status "Found section: $section" "SUCCESS"
    else
        print_status "Missing section: $section" "ERROR"
        exit 1
    fi
done

# Validate secrets encoding structure
print_status "Validating secrets encoding structure..." "INFO"

if grep -q "base64 -w 0" "$workflow_file"; then
    print_status "Base64 encoding command found" "SUCCESS"
else
    print_status "Base64 encoding command missing" "ERROR"
    exit 1
fi

# Check for managed database parameters
db_params=(
    "DB_USER"
    "DB_PASSWORD"
    "DB_HOST"
    "DB_PORT"
    "DB_NAME"
    "DB_SSL_MODE"
)

for param in "${db_params[@]}"; do
    if grep -q "$param" "$workflow_file"; then
        print_status "Found database parameter: $param" "SUCCESS"
    else
        print_status "Missing database parameter: $param" "ERROR"
        exit 1
    fi
done

# Validate payload structure
print_status "Validating payload structure..." "INFO"

payload_fields=(
    "app_name"
    "project_id"
    "application_type"
    "environment"
    "docker_image"
    "docker_tag"
    "source_repo"
    "source_branch"
    "commit_sha"
    "secrets_encoded"
)

for field in "${payload_fields[@]}"; do
    if grep -q "$field" "$workflow_file"; then
        print_status "Found payload field: $field" "SUCCESS"
    else
        print_status "Missing payload field: $field" "ERROR"
        exit 1
    fi
done

# Test secrets encoding
print_status "Testing secrets encoding..." "INFO"

# Create test secrets JSON
test_secrets_json='{
  "JWT_SECRET": "test-jwt-secret",
  "DB_USER": "spring_dev_user",
  "DB_PASSWORD": "AVNS_0bYzt0GZdky7rnP8Kl7",
  "DB_HOST": "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com",
  "DB_PORT": "25060",
  "DB_NAME": "spring_dev_db",
  "DB_SSL_MODE": "require",
  "SMTP_USER": "<EMAIL>",
  "SMTP_PASS": "test-smtp-pass",
  "GOOGLE_CLIENT_ID": "test-google-client-id",
  "GOOGLE_CLIENT_SECRET": "test-google-client-secret"
}'

# Test base64 encoding
encoded_secrets=$(echo -n "$test_secrets_json" | base64 -w 0)

if [ ${#encoded_secrets} -gt 0 ]; then
    print_status "Secrets encoding test passed (${#encoded_secrets} characters)" "SUCCESS"
else
    print_status "Secrets encoding test failed" "ERROR"
    exit 1
fi

# Test base64 decoding
decoded_secrets=$(echo "$encoded_secrets" | base64 -d)

if echo "$decoded_secrets" | jq . > /dev/null 2>&1; then
    print_status "Secrets decoding test passed (valid JSON)" "SUCCESS"
else
    print_status "Secrets decoding test failed (invalid JSON)" "ERROR"
    exit 1
fi

# Validate Python script compatibility
print_status "Validating Python script compatibility..." "INFO"

python_script="scripts/generate-manifests-cicd.py"

# Check for managed database support
if grep -q "spring_dev_user" "$python_script"; then
    print_status "Python script has managed database defaults" "SUCCESS"
else
    print_status "Python script missing managed database defaults" "ERROR"
    exit 1
fi

# Check for secrets decoding
if grep -q "decode_secrets_payload" "$python_script"; then
    print_status "Python script has secrets decoding function" "SUCCESS"
else
    print_status "Python script missing secrets decoding function" "ERROR"
    exit 1
fi

# Validate GitOps workflow compatibility
print_status "Validating GitOps workflow compatibility..." "INFO"

gitops_workflow=".github/workflows/deploy-from-cicd.yaml"

if grep -q "secrets-encoded" "$gitops_workflow"; then
    print_status "GitOps workflow supports secrets-encoded field" "SUCCESS"
else
    print_status "GitOps workflow missing secrets-encoded support" "ERROR"
    exit 1
fi

# Test payload validation
print_status "Testing payload validation..." "INFO"

# Create test payload
test_payload='{
  "app_name": "ai-spring-backend-saipriya",
  "project_id": "ai-spring-backend-saipriya",
  "application_type": "springboot-backend",
  "environment": "dev",
  "docker_image": "registry.digitalocean.com/doks-registry/ai-spring-backend-saipriya",
  "docker_tag": "v20241225-abc123",
  "source_repo": "ChidhagniConsulting/ai-spring-backend",
  "source_branch": "main",
  "commit_sha": "abc123def456",
  "secrets_encoded": "'$encoded_secrets'"
}'

if echo "$test_payload" | jq . > /dev/null 2>&1; then
    print_status "Test payload validation passed" "SUCCESS"
else
    print_status "Test payload validation failed" "ERROR"
    exit 1
fi

# Summary
echo ""
print_status "All validation tests passed!" "SUCCESS"
echo ""
print_status "Spring Boot GitOps workflow is properly configured for:" "INFO"
echo "  ✅ Managed DigitalOcean database integration"
echo "  ✅ Automatic secrets encoding and transmission"
echo "  ✅ Proper payload structure for ai-spring-backend-saipriya"
echo "  ✅ GitHub REST API dispatch events"
echo "  ✅ Cross-repository deployment automation"
echo ""
print_status "Next steps:" "INFO"
echo "  1. Copy examples/spring-boot-cicd-workflow.yml to your Spring Boot repository"
echo "  2. Configure required GitHub secrets (GITOPS_DEPLOY_TOKEN, DIGITALOCEAN_ACCESS_TOKEN)"
echo "  3. Customize app_name and project_id in the workflow"
echo "  4. Test with a merge commit to main branch"
echo "  5. Monitor deployment in ArgoCD"
echo ""
print_status "For more information, see docs/spring-boot-managed-database-integration.md" "INFO"
