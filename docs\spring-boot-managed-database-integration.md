# Spring Boot GitOps Integration with Managed Database

This guide explains how to integrate Spring Boot applications with the GitOps automation system using managed DigitalOcean database secrets.

## Overview

The updated GitOps workflow now supports:
- ✅ Managed DigitalOcean PostgreSQL database integration
- ✅ Automatic secrets encoding and transmission
- ✅ Spring Boot-specific payload structure
- ✅ Secure secrets management through GitHub Secrets
- ✅ Cross-repository deployment automation

## Required GitHub Secrets

Set up the following secrets in your Spring Boot application repository:

### Required Secrets
- `GITOPS_DEPLOY_TOKEN` - GitHub token with repo and workflow permissions for ChidhagniConsulting/gitops-argocd-apps
- `DIGITALOCEAN_ACCESS_TOKEN` - DigitalOcean container registry access token

### Optional Database Secrets (will use managed database defaults if not provided)
- `DB_USER` - Database username (default: `spring_dev_user`)
- `DB_PASSWORD` - Database password (default: `AVNS_0bYzt0GZdky7rnP8Kl7`)
- `DB_HOST` - Database host (default: `private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com`)
- `DB_PORT` - Database port (default: `25060`)
- `DB_NAME` - Database name (default: `spring_dev_db`)
- `DB_SSL_MODE` - SSL mode (default: `require`)

### Optional Application Secrets (will use defaults if not provided)
- `JWT_SECRET` - JWT signing secret (default: `supersecretkey`)
- `SMTP_USER` - SMTP username (default: `<EMAIL>`)
- `SMTP_PASS` - SMTP password (default: `fqactehafmzlltzz`)
- `GOOGLE_CLIENT_ID` - Google OAuth client ID
- `GOOGLE_CLIENT_SECRET` - Google OAuth client secret

## Workflow Implementation

### 1. Secrets Encoding Step

The workflow creates a base64-encoded JSON payload containing all secrets:

```yaml
- name: 🔐 Create Managed Database Secrets Payload
  id: create-secrets
  run: |
    SECRETS_JSON=$(cat << EOF | base64 -w 0
    {
      "JWT_SECRET": "${{ secrets.JWT_SECRET || 'supersecretkey' }}",
      "DB_USER": "${{ secrets.DB_USER || 'spring_dev_user' }}",
      "DB_PASSWORD": "${{ secrets.DB_PASSWORD || 'AVNS_0bYzt0GZdky7rnP8Kl7' }}",
      "DB_HOST": "${{ secrets.DB_HOST || 'private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com' }}",
      "DB_PORT": "${{ secrets.DB_PORT || '25060' }}",
      "DB_NAME": "${{ secrets.DB_NAME || 'spring_dev_db' }}",
      "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE || 'require' }}",
      "SMTP_USER": "${{ secrets.SMTP_USER || '<EMAIL>' }}",
      "SMTP_PASS": "${{ secrets.SMTP_PASS || 'fqactehafmzlltzz' }}",
      "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID || '1073981864538-3uiik72ohsfr2ouioror3fm1jqc493os.apps.googleusercontent.com' }}",
      "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET || '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' }}"
    }
    EOF
    )
    
    echo "secrets-encoded=${SECRETS_JSON}" >> $GITHUB_OUTPUT
```

### 2. Repository Dispatch Payload

The payload structure matches the specified format:

```javascript
const payload = {
  app_name: 'ai-spring-backend-saipriya',
  project_id: 'ai-spring-backend-saipriya', 
  application_type: 'springboot-backend',
  environment: environment,
  docker_image: dockerImage,
  docker_tag: dockerTag,
  source_repo: `${context.repo.owner}/${context.repo.repo}`,
  source_branch: '${{ github.ref_name }}',
  commit_sha: context.sha,
  secrets_encoded: secretsEncoded || ''
};
```

### 3. GitHub REST API Dispatch

```javascript
await github.rest.repos.createDispatchEvent({
  owner: 'ChidhagniConsulting',
  repo: 'gitops-argocd-apps', 
  event_type: 'deploy-to-argocd',
  client_payload: payload
});
```

## Complete Workflow Example

See `examples/spring-boot-cicd-workflow.yml` for a complete implementation.

## Key Features

### Managed Database Integration
- Automatically connects to managed DigitalOcean PostgreSQL
- SSL-enabled connections with `require` mode
- Centralized database for all applications
- No self-hosted PostgreSQL containers needed

### Secrets Management
- Base64-encoded secrets payload
- Fallback to secure defaults if secrets not provided
- Support for custom database configurations
- Secure transmission through GitHub Actions

### Deployment Automation
- Triggered only on merge commits to main branch
- Automatic Docker image building and pushing
- GitOps manifest generation and deployment
- ArgoCD integration for cluster deployment

## Environment Configuration

The workflow supports different environments:

- **dev**: Default environment for main branch merges
- **staging**: Can be configured for specific branches
- **production**: Can be configured for release tags

## Troubleshooting

### Common Issues

1. **Missing GITOPS_DEPLOY_TOKEN**
   - Ensure token has `repo` and `workflow` scopes
   - Verify token is added to repository secrets

2. **Database Connection Issues**
   - Check managed database credentials in secrets
   - Verify SSL mode is set to `require`
   - Ensure database host and port are correct

3. **Dispatch Event Not Triggering**
   - Verify merge commit detection logic
   - Check repository and event type names
   - Ensure GitOps repository is accessible

### Validation Steps

1. Check workflow logs for secrets encoding step
2. Verify payload structure in dispatch event
3. Monitor GitOps repository for triggered workflows
4. Check ArgoCD for application deployment status

## Security Considerations

- All secrets are base64-encoded for transmission
- Database credentials use managed DigitalOcean service
- SSL connections required for database access
- GitHub tokens have minimal required permissions
- Secrets are not logged in workflow output

## Migration from Self-Hosted PostgreSQL

If migrating from self-hosted PostgreSQL:

1. Update application configuration to use managed database
2. Remove PostgreSQL deployment manifests
3. Update connection strings to include SSL mode
4. Test database connectivity with new credentials
5. Deploy using the new workflow

## Next Steps

1. Copy the workflow template to your Spring Boot repository
2. Configure required GitHub secrets
3. Test with a merge commit to main branch
4. Monitor deployment in ArgoCD
5. Verify application connectivity to managed database
