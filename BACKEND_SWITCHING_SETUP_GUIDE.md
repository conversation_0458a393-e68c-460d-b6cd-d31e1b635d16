# 🔄 Backend Switching Setup Guide

## Overview
This guide provides step-by-step instructions to set up dynamic backend switching for the AI React Frontend using local aliases and scripts. This approach combines GitOps infrastructure management with direct operational commands for maximum flexibility.

## 🎯 Architecture Overview

- **GitOps**: Manages infrastructure (ConfigMaps, Deployments, Services)
- **Local Scripts**: Handle runtime backend switching operations
- **Aliases**: Provide clean, memorable commands for daily operations

---

## 📋 Prerequisites

- `kubectl` configured with access to your Kubernetes cluster
- `jq` installed for JSON processing
- `curl` for health checks
- Bash shell (Linux/macOS/WSL)

```bash
# Install prerequisites (Ubuntu/Debian)
sudo apt update
sudo apt install jq curl

# Install prerequisites (macOS)
brew install jq curl

# Verify kubectl access
kubectl get nodes
```

---

## 🚀 Step 1: GitOps Infrastructure Setup

### 1.1 Create Runtime Config ConfigMap

Create the file `ai-react-frontend/k8s/runtime-config-configmap.yaml`:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-runtime-config
  namespace: ai-react-frontend-dev
  labels:
    app: ai-react-frontend
    component: runtime-config
    managed-by: argocd
    version: v1.0.0
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    description: "Runtime backend configuration for dynamic switching"
data:
  runtime-config.json: |
    {
      "currentBackend": "spring",
      "backendUrl": "http://*************:8080",
      "environment": "dev",
      "serviceName": "ai-spring-backend-service",
      "namespace": "ai-spring-backend-dev",
      "apiVersion": "v1",
      "lastUpdated": "2024-01-01T00:00:00Z"
    }
```

### 1.2 Update Deployment Configuration

Update `ai-react-frontend/k8s/deployment.yaml` to include:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-react-frontend
  namespace: ai-react-frontend-dev
spec:
  template:
    spec:
      containers:
      - name: ai-react-frontend
        image: your-registry/ai-react-frontend:latest
        env:
        - name: VITE_APP_ENV
          value: "dev"
        - name: VITE_APP_SERVICE_NAME
          value: "ai-spring-backend-service"
        - name: VITE_APP_BACKEND_NAMESPACE
          value: "ai-spring-backend-dev"
        - name: VITE_USE_RUNTIME_CONFIG
          value: "true"
        volumeMounts:
        - name: runtime-config
          mountPath: /etc/nginx/config
          readOnly: true
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
          readOnly: true
      volumes:
      - name: runtime-config
        configMap:
          name: ai-react-frontend-runtime-config
      - name: nginx-config
        configMap:
          name: ai-react-frontend-nginx-config
```

### 1.3 Update Nginx Configuration

Update `ai-react-frontend/k8s/nginx-configmap.yaml` to add the `/api/config` endpoint:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-react-frontend-nginx-config
  namespace: ai-react-frontend-dev
data:
  default.conf: |
    server {
        listen 3000;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Runtime configuration endpoint
        location /api/config {
            alias /etc/nginx/config/runtime-config.json;
            add_header Content-Type application/json;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type";
        }

        # Handle preflight requests
        location /api/config {
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, OPTIONS";
                add_header Access-Control-Allow-Headers "Content-Type";
                add_header Content-Length 0;
                return 204;
            }
        }

        # React app
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        # Static assets with caching
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
```

### 1.4 Commit and Deploy via GitOps

```bash
cd gitops-argocd-apps
git checkout -b feature/backend-switching-setup

# Add new and updated files
git add ai-react-frontend/k8s/runtime-config-configmap.yaml
git add ai-react-frontend/k8s/deployment.yaml
git add ai-react-frontend/k8s/nginx-configmap.yaml

git commit -m "feat: Add runtime backend switching infrastructure

- Add runtime-config-configmap.yaml for dynamic backend configuration
- Update deployment.yaml with ConfigMap volume mount and environment variables
- Update nginx-configmap.yaml with /api/config endpoint for runtime configuration
- Enable dynamic backend switching without image rebuilds

Supported backends:
- Spring Boot: http://*************:8080
- Django: http://*************:8000
- NestJS: http://**************:3000"

git push origin feature/backend-switching-setup
```

### 1.5 Sync via ArgoCD

```bash
# Sync via ArgoCD CLI
argocd app sync ai-react-frontend
argocd app wait ai-react-frontend --timeout 300

# Or sync via ArgoCD UI
# Navigate to ArgoCD dashboard → ai-react-frontend → Sync
```

---

## 🛠️ Step 2: Local Scripts Setup

### 2.1 Create Scripts Directory

```bash
mkdir -p ~/k8s-scripts
cd ~/k8s-scripts
```

### 2.2 Create Main Backend Switch Script

Create `~/k8s-scripts/switch-backend.sh`:

```bash
#!/bin/bash
set -e

BACKEND=$1
NAMESPACE="ai-react-frontend-dev"

if [ -z "$BACKEND" ]; then
    echo "❌ Backend type required!"
    echo "Usage: $0 {spring|django|nest}"
    exit 1
fi

case $BACKEND in
  "spring")
    URL="http://*************:8080"
    SERVICE="ai-spring-backend-service"
    NS="ai-spring-backend-dev"
    HEALTH_ENDPOINT="/actuator/health"
    ;;
  "django")
    URL="http://*************:8000"
    SERVICE="ai-django-backend-service"
    NS="ai-django-backend-dev"
    HEALTH_ENDPOINT="/health"
    ;;
  "nest")
    URL="http://**************:3000"
    SERVICE="ai-nest-backend-service"
    NS="ai-nest-backend-dev"
    HEALTH_ENDPOINT="/health"
    ;;
  *)
    echo "❌ Invalid backend type: $BACKEND"
    echo "Usage: $0 {spring|django|nest}"
    exit 1
    ;;
esac

echo "🔄 Switching to $BACKEND backend..."

# Update ConfigMap
kubectl patch configmap ai-react-frontend-runtime-config -n $NAMESPACE --type merge -p "{
  \"data\": {
    \"runtime-config.json\": \"{\\\"currentBackend\\\":\\\"$BACKEND\\\",\\\"backendUrl\\\":\\\"$URL\\\",\\\"environment\\\":\\\"dev\\\",\\\"serviceName\\\":\\\"$SERVICE\\\",\\\"namespace\\\":\\\"$NS\\\",\\\"apiVersion\\\":\\\"v1\\\",\\\"lastUpdated\\\":\\\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\\\"}\"
  }
}"

# Restart frontend deployment
kubectl rollout restart deployment ai-react-frontend -n $NAMESPACE

# Wait for rollout to complete
echo "⏳ Waiting for deployment to complete..."
kubectl rollout status deployment ai-react-frontend -n $NAMESPACE --timeout=300s

# Test backend health
echo "🔍 Testing backend health..."
if curl -f -s "$URL$HEALTH_ENDPOINT" > /dev/null 2>&1; then
    echo "✅ $BACKEND backend is healthy ($URL)"
else
    echo "⚠️  $BACKEND backend may not be responding ($URL)"
fi

# Show current config
echo "📋 Current configuration:"
kubectl get configmap ai-react-frontend-runtime-config -n $NAMESPACE -o jsonpath='{.data.runtime-config\.json}' | jq .

echo "🎉 Successfully switched to $BACKEND backend!"
```

### 2.3 Create Health Check Script

Create `~/k8s-scripts/backend-health.sh`:

```bash
#!/bin/bash
echo "🏥 Backend Health Check"
echo "======================"

# Get current config
CONFIG=$(kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}')
CURRENT_BACKEND=$(echo $CONFIG | jq -r .currentBackend)
CURRENT_URL=$(echo $CONFIG | jq -r .backendUrl)

echo "Current Backend: $CURRENT_BACKEND"
echo "Current URL: $CURRENT_URL"
echo ""

# Test all backends
echo "Testing all backends:"
echo "--------------------"

# Spring
echo -n "Spring (*************:8080): "
curl -f -s http://*************:8080/actuator/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Down"

# Django  
echo -n "Django (*************:8000): "
curl -f -s http://*************:8000/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Down"

# NestJS
echo -n "NestJS (**************:3000): "
curl -f -s http://**************:3000/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Down"

echo ""
echo "Frontend URL: http://*************:3000"
echo "Config API: http://*************:3000/api/config"
```

### 2.4 Set Script Permissions

```bash
chmod +x ~/k8s-scripts/switch-backend.sh
chmod +x ~/k8s-scripts/backend-health.sh
```

---

## 🎯 Step 3: Shell Aliases Setup

### 3.1 Add Aliases to Shell Profile

Add the following to your `~/.bashrc` (Linux) or `~/.zshrc` (macOS):

```bash
# Backend switching aliases
alias switch-spring="~/k8s-scripts/switch-backend.sh spring"
alias switch-django="~/k8s-scripts/switch-backend.sh django"
alias switch-nest="~/k8s-scripts/switch-backend.sh nest"

# Status and monitoring aliases
alias current-backend="kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq .currentBackend"
alias backend-status="kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}' | jq ."
alias health-check="~/k8s-scripts/backend-health.sh"
alias frontend-logs="kubectl logs -f deployment/ai-react-frontend -n ai-react-frontend-dev"

# Quick access aliases
alias frontend-pods="kubectl get pods -n ai-react-frontend-dev -l app=ai-react-frontend"
alias frontend-config="curl -s http://*************:3000/api/config | jq ."
```

### 3.2 Reload Shell Configuration

```bash
# For bash
source ~/.bashrc

# For zsh
source ~/.zshrc
```

---

## 🚀 Step 4: Usage Guide

### 4.1 Backend Switching Commands

```bash
# Switch to different backends
switch-spring    # Switch to Spring Boot backend
switch-django    # Switch to Django backend
switch-nest      # Switch to NestJS backend
```

### 4.2 Status and Monitoring Commands

```bash
# Check current backend
current-backend

# View full backend configuration
backend-status

# Check health of all backends
health-check

# Monitor frontend logs
frontend-logs

# Check frontend pods
frontend-pods

# Test frontend config API
frontend-config
```

### 4.3 Example Usage Session

```bash
# Check current status
$ current-backend
"spring"

# Check health of all backends
$ health-check
🏥 Backend Health Check
======================
Current Backend: spring
Current URL: http://*************:8080

Testing all backends:
--------------------
Spring (*************:8080): ✅ Healthy
Django (*************:8000): ✅ Healthy
NestJS (**************:3000): ❌ Down

Frontend URL: http://*************:3000
Config API: http://*************:3000/api/config

# Switch to Django
$ switch-django
🔄 Switching to django backend...
configmap/ai-react-frontend-runtime-config patched
deployment.apps/ai-react-frontend restarted
⏳ Waiting for deployment to complete...
deployment "ai-react-frontend" successfully rolled out
🔍 Testing backend health...
✅ django backend is healthy (http://*************:8000)
📋 Current configuration:
{
  "currentBackend": "django",
  "backendUrl": "http://*************:8000",
  "environment": "dev",
  "serviceName": "ai-django-backend-service",
  "namespace": "ai-django-backend-dev",
  "apiVersion": "v1",
  "lastUpdated": "2024-01-15T10:30:45Z"
}
🎉 Successfully switched to django backend!

# Verify the switch
$ frontend-config
{
  "currentBackend": "django",
  "backendUrl": "http://*************:8000",
  "environment": "dev",
  "serviceName": "ai-django-backend-service",
  "namespace": "ai-django-backend-dev",
  "apiVersion": "v1",
  "lastUpdated": "2024-01-15T10:30:45Z"
}
```

---

## 🧪 Step 5: Testing and Verification

### 5.1 Test Infrastructure Deployment

```bash
# Verify ConfigMap exists
kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev

# Check deployment has correct volumes
kubectl describe deployment ai-react-frontend -n ai-react-frontend-dev | grep -A 10 "Volumes:"

# Test config API endpoint
curl http://*************:3000/api/config | jq .
```

### 5.2 Test Backend Switching

```bash
# Test each backend switch
switch-spring
sleep 30
switch-django  
sleep 30
switch-nest
sleep 30
switch-spring

# Verify each switch worked
current-backend
frontend-config
```

### 5.3 Test Health Monitoring

```bash
# Run comprehensive health check
health-check

# Monitor logs during switch
frontend-logs &
switch-django
# Ctrl+C to stop log monitoring
```

---

## 🔧 Troubleshooting

### Common Issues and Solutions

#### 1. **ConfigMap Not Found**
```bash
# Check if ConfigMap exists
kubectl get configmap -n ai-react-frontend-dev

# If missing, apply manually
kubectl apply -f ai-react-frontend/k8s/runtime-config-configmap.yaml
```

#### 2. **Permission Denied on Scripts**
```bash
# Fix script permissions
chmod +x ~/k8s-scripts/*.sh
```

#### 3. **Aliases Not Working**
```bash
# Reload shell configuration
source ~/.bashrc  # or ~/.zshrc

# Check if aliases are loaded
alias | grep switch
```

#### 4. **Backend Health Check Fails**
```bash
# Test backend URLs manually
curl -v http://*************:8080/actuator/health
curl -v http://*************:8000/health
curl -v http://**************:3000/health
```

#### 5. **Frontend Not Updating**
```bash
# Force restart frontend
kubectl rollout restart deployment ai-react-frontend -n ai-react-frontend-dev

# Check frontend logs
kubectl logs deployment/ai-react-frontend -n ai-react-frontend-dev
```

### Debug Commands

```bash
# Check current ConfigMap content
kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o yaml

# Describe deployment for volume mounts
kubectl describe deployment ai-react-frontend -n ai-react-frontend-dev

# Check ArgoCD application status
argocd app get ai-react-frontend

# Test config endpoint with verbose output
curl -v http://*************:3000/api/config
```

---

## 📋 Environment-Specific Configurations

### Development Environment
- **Current Setup**: External IPs for backends
- **Frontend**: http://*************:3000
- **Spring**: http://*************:8080
- **Django**: http://*************:8000
- **NestJS**: http://**************:3000

### Staging Environment (Future)
Update script variables for staging:
```bash
NAMESPACE="ai-react-frontend-staging"
# Use internal cluster URLs:
# http://ai-spring-backend-service.ai-spring-backend-staging.svc.cluster.local:8080
```

### Production Environment (Future)
Update script variables for production:
```bash
NAMESPACE="ai-react-frontend-prod"
# Use internal cluster URLs:
# http://ai-spring-backend-service.ai-spring-backend-prod.svc.cluster.local:8080
```

---

## ✅ Success Criteria

After completing this setup, you should be able to:

1. ✅ Switch backends using simple aliases (`switch-django`, `switch-spring`, `switch-nest`)
2. ✅ Monitor backend health with `health-check`
3. ✅ View current configuration with `current-backend` and `backend-status`
4. ✅ Access runtime config via frontend API at `/api/config`
5. ✅ See immediate frontend updates after backend switches
6. ✅ Monitor operations with `frontend-logs`

---

## 🎉 Next Steps

1. **Team Onboarding**: Share this guide with team members
2. **Documentation**: Add to team wiki or documentation site
3. **Automation**: Consider integrating with CI/CD pipelines
4. **Monitoring**: Set up alerts for backend health checks
5. **Scaling**: Extend to staging and production environments

---

## 📞 Support

If you encounter issues:

1. **Check Prerequisites**: Ensure `kubectl`, `jq`, and `curl` are installed
2. **Verify Permissions**: Confirm kubectl access and script permissions
3. **Review Logs**: Check ArgoCD, frontend, and backend logs
4. **Test Manually**: Use direct kubectl commands to isolate issues
5. **Health Check**: Run `health-check` to verify backend status

For additional help, create an issue with the `support` label and include:
- Error messages
- Output of `kubectl get pods -A`
- Output of `health-check`
- ArgoCD application status