#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Enable automatic ArgoCD deployment for GitOps automation
    
.DESCRIPTION
    This script helps enable the automatic ArgoCD deployment feature by setting
    the required repository variable and verifying the ARC runner setup.
    
.PARAMETER Verify
    Only verify the current configuration without making changes
    
.PARAMETER Disable
    Disable automatic deployment
    
.EXAMPLE
    ./enable-auto-deploy.ps1
    
.EXAMPLE
    ./enable-auto-deploy.ps1 -Verify
    
.EXAMPLE
    ./enable-auto-deploy.ps1 -Disable
#>

param(
    [Parameter(Mandatory=$false)]
    [switch]$Verify,
    
    [Parameter(Mandatory=$false)]
    [switch]$Disable
)

# Function to write colored output
function Write-Status {
    param(
        [string]$Message,
        [string]$Type = "INFO"
    )
    
    switch ($Type) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
        "HEADER"  { Write-Host "🚀 $Message" -ForegroundColor Magenta }
    }
}

Write-Status "GitOps Auto-Deployment Configuration" "HEADER"
Write-Host "====================================" -ForegroundColor Magenta
Write-Host ""

if ($Verify) {
    Write-Status "Verifying auto-deployment configuration..." "INFO"
    
    # Check if ARC runners are available
    Write-Host "🔍 Checking ARC runner status..." -ForegroundColor Yellow
    try {
        $runners = kubectl get runnerdeployment gitops-argocd-runner -n arc-system -o jsonpath='{.status.availableReplicas}' 2>$null
        if ($LASTEXITCODE -eq 0 -and $runners -gt 0) {
            Write-Status "ARC runners are available ($runners replicas)" "SUCCESS"
        } else {
            Write-Status "ARC runners are not available or not running" "ERROR"
        }
    } catch {
        Write-Status "Failed to check ARC runner status" "ERROR"
    }
    
    # Check kubectl access
    Write-Host "🔍 Checking kubectl access..." -ForegroundColor Yellow
    try {
        $null = kubectl cluster-info --request-timeout=5s 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "kubectl cluster access is working" "SUCCESS"
        } else {
            Write-Status "kubectl cluster access failed" "ERROR"
        }
    } catch {
        Write-Status "kubectl is not available" "ERROR"
    }
    
    # Check ArgoCD access
    Write-Host "🔍 Checking ArgoCD access..." -ForegroundColor Yellow
    try {
        $null = kubectl get crd applications.argoproj.io 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "ArgoCD CRDs are available" "SUCCESS"
        } else {
            Write-Status "ArgoCD CRDs not found" "ERROR"
        }
    } catch {
        Write-Status "Failed to check ArgoCD access" "ERROR"
    }
    
    Write-Host ""
    Write-Status "Configuration verification completed" "INFO"
    Write-Host ""
    Write-Host "📋 To enable auto-deployment:" -ForegroundColor Cyan
    Write-Host "1. Go to GitHub repository Settings" -ForegroundColor White
    Write-Host "2. Navigate to Secrets and variables → Actions → Variables" -ForegroundColor White
    Write-Host "3. Create variable: ENABLE_AUTO_DEPLOY = true" -ForegroundColor White
    Write-Host "4. Create a test deployment issue to verify functionality" -ForegroundColor White
    
} elseif ($Disable) {
    Write-Status "Auto-deployment disable instructions" "WARNING"
    Write-Host ""
    Write-Host "📋 To disable auto-deployment:" -ForegroundColor Yellow
    Write-Host "1. Go to GitHub repository Settings" -ForegroundColor White
    Write-Host "2. Navigate to Secrets and variables → Actions → Variables" -ForegroundColor White
    Write-Host "3. Set ENABLE_AUTO_DEPLOY = false (or delete the variable)" -ForegroundColor White
    Write-Host ""
    Write-Status "Auto-deployment will be disabled for new deployment issues" "INFO"
    
} else {
    Write-Status "Setting up auto-deployment configuration..." "INFO"
    
    # Verify prerequisites
    Write-Host "🔍 Verifying prerequisites..." -ForegroundColor Yellow
    
    $prerequisitesMet = $true
    
    # Check ARC runners
    try {
        $runners = kubectl get runnerdeployment gitops-argocd-runner -n arc-system -o jsonpath='{.status.availableReplicas}' 2>$null
        if ($LASTEXITCODE -eq 0 -and $runners -gt 0) {
            Write-Status "ARC runners are available ($runners replicas)" "SUCCESS"
        } else {
            Write-Status "ARC runners are not available. Deploy ARC runners first." "ERROR"
            $prerequisitesMet = $false
        }
    } catch {
        Write-Status "Failed to check ARC runner status. Ensure kubectl is configured." "ERROR"
        $prerequisitesMet = $false
    }
    
    # Check ArgoCD
    try {
        $null = kubectl get crd applications.argoproj.io 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "ArgoCD is installed and accessible" "SUCCESS"
        } else {
            Write-Status "ArgoCD is not installed or accessible" "ERROR"
            $prerequisitesMet = $false
        }
    } catch {
        Write-Status "Failed to verify ArgoCD installation" "ERROR"
        $prerequisitesMet = $false
    }
    
    if (-not $prerequisitesMet) {
        Write-Host ""
        Write-Status "Prerequisites not met. Please resolve the issues above before enabling auto-deployment." "ERROR"
        Write-Host ""
        Write-Host "📋 Required setup:" -ForegroundColor Yellow
        Write-Host "1. Deploy ARC runners: kubectl apply -f arc-config/" -ForegroundColor White
        Write-Host "2. Ensure ArgoCD is installed in the cluster" -ForegroundColor White
        Write-Host "3. Verify kubectl access to the cluster" -ForegroundColor White
        exit 1
    }
    
    Write-Host ""
    Write-Status "Prerequisites verified successfully!" "SUCCESS"
    Write-Host ""
    Write-Host "📋 Manual steps to enable auto-deployment:" -ForegroundColor Green
    Write-Host "1. Go to your GitHub repository: https://github.com/ChidhagniConsulting/gitops-argocd-apps" -ForegroundColor White
    Write-Host "2. Navigate to Settings → Secrets and variables → Actions → Variables" -ForegroundColor White
    Write-Host "3. Click 'New repository variable'" -ForegroundColor White
    Write-Host "4. Set Name: ENABLE_AUTO_DEPLOY" -ForegroundColor White
    Write-Host "5. Set Value: true" -ForegroundColor White
    Write-Host "6. Click 'Add variable'" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 Test the setup:" -ForegroundColor Green
    Write-Host "1. Create a new deployment issue using the template" -ForegroundColor White
    Write-Host "2. Verify the workflow runs on ARC runners" -ForegroundColor White
    Write-Host "3. Check that ArgoCD application is automatically deployed" -ForegroundColor White
    Write-Host "4. Monitor the application in ArgoCD dashboard" -ForegroundColor White
    Write-Host ""
    Write-Status "Auto-deployment setup instructions provided" "SUCCESS"
}

Write-Host ""
Write-Host "📚 Additional Resources:" -ForegroundColor Cyan
Write-Host "- Monitor runners: .\scripts\monitor-arc-runners.ps1" -ForegroundColor White
Write-Host "- Test ARC setup: .\scripts\test-minikube-arc.ps1" -ForegroundColor White
Write-Host "- ARC Integration Guide: docs\ARC_INTEGRATION_GUIDE.md" -ForegroundColor White
