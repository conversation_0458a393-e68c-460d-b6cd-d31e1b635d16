#!/bin/bash
echo "🏥 Backend Health Check"
echo "======================"

# Get current config
CONFIG=$(kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}')
CURRENT_BACKEND=$(echo $CONFIG | jq -r .currentBackend)
CURRENT_URL=$(echo $CONFIG | jq -r .backendUrl)

echo "Current Backend: $CURRENT_BACKEND"
echo "Current URL: $CURRENT_URL"
echo ""

# Test all backends
echo "Testing all backends:"
echo "--------------------"

# Spring
echo -n "Spring (64.225.85.162:8080): "
curl -f -s http://64.225.85.162:8080/actuator/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Down"

# Django  
echo -n "Django (152.42.156.72:8000): "
curl -f -s http://152.42.156.72:8000/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Down"

# NestJS
echo -n "NestJS (174.138.121.78:3000): "
curl -f -s http://174.138.121.78:3000/health > /dev/null 2>&1 && echo "✅ Healthy" || echo "❌ Down"

echo ""
echo "Frontend URL: http://64.225.85.157:3000"
echo "Config API: http://64.225.85.157:3000/api/config"
