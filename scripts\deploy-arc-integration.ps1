#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Deploy ARC integration for GitOps ArgoCD repository
    
.DESCRIPTION
    This script deploys the Actions Runner Controller (ARC) integration for the
    GitOps ArgoCD repository. It applies RBAC configuration, deploys the runner
    scale set, and verifies the integration.
    
.PARAMETER DryRun
    Show what would be deployed without actually executing kubectl commands
    
.PARAMETER SkipVerification
    Skip the verification steps after deployment
    
.PARAMETER Force
    Force deployment even if resources already exist
    
.EXAMPLE
    ./deploy-arc-integration.ps1
    
.EXAMPLE
    ./deploy-arc-integration.ps1 -DryRun
    
.EXAMPLE
    ./deploy-arc-integration.ps1 -Force -SkipVerification
#>

param(
    [Parameter(Mandatory=$false)]
    [switch]$DryRun,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipVerification,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-Status {
    param(
        [string]$Message,
        [string]$Type = "INFO"
    )
    
    switch ($Type) {
        "SUCCESS" { Write-Host "✅ $Message" -ForegroundColor Green }
        "ERROR"   { Write-Host "❌ $Message" -ForegroundColor Red }
        "WARNING" { Write-Host "⚠️  $Message" -ForegroundColor Yellow }
        "INFO"    { Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
        "STEP"    { Write-Host "🚀 $Message" -ForegroundColor Magenta }
    }
}

# Function to execute kubectl command
function Invoke-Kubectl {
    param(
        [string]$Command,
        [string]$Description,
        [bool]$ContinueOnError = $false
    )
    
    Write-Status "Executing: kubectl $Command" "INFO"
    
    if ($DryRun) {
        Write-Host "   [DRY RUN] kubectl $Command" -ForegroundColor Yellow
        return $true
    }
    
    try {
        $output = Invoke-Expression "kubectl $Command" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Status "$Description completed successfully" "SUCCESS"
            if ($output) {
                Write-Host "   Output: $output" -ForegroundColor Gray
            }
            return $true
        } else {
            if ($ContinueOnError) {
                Write-Status "$Description failed but continuing: $output" "WARNING"
                return $false
            } else {
                Write-Status "$Description failed: $output" "ERROR"
                throw "kubectl command failed"
            }
        }
    } catch {
        if ($ContinueOnError) {
            Write-Status "$Description failed but continuing: $($_.Exception.Message)" "WARNING"
            return $false
        } else {
            Write-Status "$Description failed: $($_.Exception.Message)" "ERROR"
            throw
        }
    }
}

# Function to check if resource exists
function Test-KubernetesResource {
    param(
        [string]$ResourceType,
        [string]$ResourceName,
        [string]$Namespace = ""
    )
    
    try {
        $namespaceArg = if ($Namespace) { "-n $Namespace" } else { "" }
        $null = Invoke-Expression "kubectl get $ResourceType $ResourceName $namespaceArg" 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

Write-Host "🚀 GitOps ArgoCD ARC Integration Deployment (DOKS)" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""

# Pre-flight checks
Write-Status "Performing pre-flight checks..." "STEP"

# Check kubectl availability
if (-not (Get-Command kubectl -ErrorAction SilentlyContinue)) {
    Write-Status "kubectl not found. Please install kubectl." "ERROR"
    exit 1
}

# Check cluster connectivity
try {
    $null = kubectl cluster-info --request-timeout=10s 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Status "Cannot connect to Kubernetes cluster" "ERROR"
        exit 1
    }
    Write-Status "Kubernetes cluster is accessible" "SUCCESS"
} catch {
    Write-Status "Failed to connect to Kubernetes cluster" "ERROR"
    exit 1
}

# Check if arc-system namespace exists
if (-not (Test-KubernetesResource "namespace" "arc-system")) {
    Write-Status "Creating arc-system namespace..." "INFO"
    Invoke-Kubectl "create namespace arc-system" "Namespace creation"
} else {
    Write-Status "arc-system namespace already exists" "INFO"
}

# Step 1: Apply RBAC Configuration
Write-Status "Deploying RBAC configuration..." "STEP"

if (-not (Test-Path "arc-config/rbac.yaml")) {
    Write-Status "RBAC configuration file not found: arc-config/rbac.yaml" "ERROR"
    exit 1
}

# Check if resources already exist
$saExists = Test-KubernetesResource "serviceaccount" "gitops-runner-sa" "ch-arc-runner-system"
$crExists = Test-KubernetesResource "clusterrole" "gitops-runner-role"
$crbExists = Test-KubernetesResource "clusterrolebinding" "gitops-runner-binding"

if (($saExists -or $crExists -or $crbExists) -and -not $Force) {
    Write-Status "RBAC resources already exist. Use -Force to overwrite." "WARNING"
    Write-Status "ServiceAccount exists: $saExists" "INFO"
    Write-Status "ClusterRole exists: $crExists" "INFO"
    Write-Status "ClusterRoleBinding exists: $crbExists" "INFO"
} else {
    Invoke-Kubectl "apply -f arc-config/rbac.yaml" "RBAC configuration deployment"
}

# Step 2: Deploy Runner Scale Set
Write-Status "Deploying runner scale set..." "STEP"

if (-not (Test-Path "arc-config/runner-scale-set.yaml")) {
    Write-Status "Runner scale set configuration file not found: arc-config/runner-scale-set.yaml" "ERROR"
    exit 1
}

# Check if runner scale set already exists
$rdExists = Test-KubernetesResource "runnerscaleset" "chidhagni-organisation-runner" "ch-arc-runner-system"

if ($rdExists -and -not $Force) {
    Write-Status "Runner deployment already exists. Use -Force to overwrite." "WARNING"
} else {
    Invoke-Kubectl "apply -f arc-config/runner-scale-set.yaml" "Runner scale set deployment"
}

# Step 3: Verification (if not skipped)
if (-not $SkipVerification -and -not $DryRun) {
    Write-Status "Verifying deployment..." "STEP"
    
    # Wait a moment for resources to be created
    Start-Sleep -Seconds 5
    
    # Verify service account
    if (Test-KubernetesResource "serviceaccount" "gitops-runner-sa" "ch-arc-runner-system") {
        Write-Status "Service account verification passed" "SUCCESS"
    } else {
        Write-Status "Service account verification failed" "ERROR"
    }

    # Verify cluster role
    if (Test-KubernetesResource "clusterrole" "gitops-runner-role") {
        Write-Status "Cluster role verification passed" "SUCCESS"
    } else {
        Write-Status "Cluster role verification failed" "ERROR"
    }

    # Verify runner scale set
    if (Test-KubernetesResource "runnerscaleset" "chidhagni-organisation-runner" "ch-arc-runner-system") {
        Write-Status "Runner scale set verification passed" "SUCCESS"

        # Check runner pods
        Write-Status "Checking runner pods..." "INFO"
        Invoke-Kubectl "get pods -n ch-arc-runner-system -l app=chidhagni-organisation-runner" "Runner pod status" $true

    } else {
        Write-Status "Runner scale set verification failed" "ERROR"
    }
    
    # Test permissions
    Write-Status "Testing service account permissions..." "INFO"
    $permissionTests = @(
        "create applications.argoproj.io",
        "get namespaces",
        "create deployments.apps",
        "get pods"
    )
    
    foreach ($permission in $permissionTests) {
        $result = Invoke-Kubectl "auth can-i $permission --as=system:serviceaccount:ch-arc-runner-system:gitops-runner-sa" "Permission test: $permission" $true
    }
}

# Step 4: Display next steps
Write-Status "Deployment completed!" "SUCCESS"
Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Green
Write-Host "1. Run the comprehensive test suite:" -ForegroundColor Cyan
Write-Host "   .\scripts\test-doks-arc.ps1" -ForegroundColor White
Write-Host ""
Write-Host "2. Monitor runner status:" -ForegroundColor Cyan
Write-Host "   kubectl get runnerscaleset -n ch-arc-runner-system" -ForegroundColor White
Write-Host "   kubectl get pods -n ch-arc-runner-system -l app=chidhagni-organisation-runner" -ForegroundColor White
Write-Host ""
Write-Host "3. Check runner logs:" -ForegroundColor Cyan
Write-Host "   kubectl logs -n ch-arc-runner-system -l app=chidhagni-organisation-runner" -ForegroundColor White
Write-Host ""
Write-Host "4. Test automation by creating a deployment issue:" -ForegroundColor Cyan
Write-Host "   https://github.com/ChidhagniConsulting/gitops-argocd-apps/issues/new/choose" -ForegroundColor White
Write-Host ""

if ($DryRun) {
    Write-Status "This was a dry run. Use without -DryRun to actually deploy." "INFO"
}

Write-Status "ARC integration deployment script completed" "SUCCESS"
exit 0
