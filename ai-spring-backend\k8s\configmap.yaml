apiVersion: v1
kind: ConfigMap
metadata:
  name: ai-spring-backend-config
  namespace: ai-spring-backend-dev
  labels:
    app: ai-spring-backend
    component: config
    environment: dev
    app-type: springboot-backend
data:
  # Application Configuration
  NODE_ENV: "dev"
  PORT: "8080"
  # Spring Boot Backend Configuration
  SPRING_PROFILES_ACTIVE: "dev"
  SERVER_PORT: "8080"
  SPRING_APPLICATION_NAME: "ai-spring-backend"
  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  # JVM Configuration
  JAVA_OPTS: "-Xms256m -Xmx512m -XX:+UseG1GC -XX:+UseContainerSupport"
  # Application URLs
  APP_URL: "http://ai-spring-backend.dev.local"
  API_URL: "http://ai-spring-backend-service:8080"
  # Spring Boot Database Configuration (Managed DigitalOcean Database)
  SPRING_DATASOURCE_URL: "**************************************************************************************************************************"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "update"
  SPRING_JPA_SHOW_SQL: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "true"
  # Spring Boot Security & JWT
  JWT_EXPIRATION: "24h"
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_REDIRECT_URI: "http://ai-spring-backend.dev.local/auth/google/callback"
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_SCOPE: "openid,profile,email"
  # Spring Boot CORS Configuration
  CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:3001"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"
  # Spring Boot Mail Configuration
  SPRING_MAIL_HOST: "smtp.gmail.com"
  SPRING_MAIL_PORT: "587"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH: "true"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE: "true"
