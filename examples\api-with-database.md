# Example: API with PostgreSQL Database

This example demonstrates deploying a REST API application with a PostgreSQL database using the self-service GitOps system.

## Application Details
- **Type**: REST API
- **Technology**: Node.js/Express, PostgreSQL
- **Environment**: Production
- **Database**: PostgreSQL with persistent storage
- **Security**: Secrets for database credentials and JWT

## Issue Template Values

```yaml
Application Name: User Management API
Project Identifier: user-api
Container Image: myorg/user-api:v2.1.0
Environment: prod
Kubernetes Namespace: user-api
Replica Count: 3
CPU Request: 200m
CPU Limit: 500m
Memory Request: 512Mi
Memory Limit: 1Gi
Container Port: 8080
Service Type: ClusterIP
Application Type: api
Health Check Path: /api/health

Enable PostgreSQL Database: ✓
Database Name: userdb
Database User: apiuser
Storage Size: 10Gi

Environment Variables:
NODE_ENV=production
API_VERSION=v2
LOG_LEVEL=info
DB_HOST=postgres-service
DB_PORT=5432
DB_NAME=userdb
JWT_EXPIRATION=86400000
CORS_ALLOWED_ORIGINS=https://app.example.com
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

Secret Keys:
JWT_SECRET
DB_PASSWORD
API_KEY
SMTP_PASSWORD
ENCRYPTION_KEY
```

## Generated Structure

```
user-api/
├── argocd/
│   ├── application.yaml
│   └── project.yaml
└── k8s/
    ├── namespace.yaml
    ├── deployment.yaml
    ├── service.yaml
    ├── configmap.yaml
    ├── secret.yaml
    ├── postgres-deployment.yaml
    ├── postgres-service.yaml
    └── postgres-pvc.yaml
```

## Pre-Deployment Setup

### 1. Update Secrets
Before deploying, update the secret values:

```bash
# Edit the secret file
vim user-api/k8s/secret.yaml

# Generate base64 encoded values
echo -n "super-secret-jwt-key" | base64
echo -n "secure-db-password" | base64
echo -n "api-key-value" | base64
echo -n "smtp-password" | base64
echo -n "encryption-key-32-chars" | base64
```

Update the secret.yaml file:
```yaml
data:
  JWT_SECRET: c3VwZXItc2VjcmV0LWp3dC1rZXk=
  DB_PASSWORD: c2VjdXJlLWRiLXBhc3N3b3Jk
  API_KEY: YXBpLWtleS12YWx1ZQ==
  SMTP_PASSWORD: c210cC1wYXNzd29yZA==
  ENCRYPTION_KEY: ZW5jcnlwdGlvbi1rZXktMzItY2hhcnM=
  DB_USER: YXBpdXNlcg==  # apiuser
```

## Deployment Steps

### 1. Deploy with ArgoCD
```bash
# Apply ArgoCD manifests
kubectl apply -f user-api/argocd/project.yaml
kubectl apply -f user-api/argocd/application.yaml
```

### 2. Monitor Deployment
```bash
# Check ArgoCD application
kubectl get application user-api -n argocd

# Monitor pods
kubectl get pods -n user-api -w

# Check deployment status
kubectl rollout status deployment/user-api -n user-api
kubectl rollout status deployment/postgres -n user-api
```

### 3. Verify Database
```bash
# Check PostgreSQL pod
kubectl get pods -l app=postgres -n user-api

# Test database connection
kubectl exec -it deployment/postgres -n user-api -- psql -U apiuser -d userdb -c "\dt"
```

## Application Configuration

### Database Schema
The application should handle database migrations. Example init script:

```sql
-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
```

### Environment Variables
The application will receive these environment variables:
- `NODE_ENV=production`
- `API_VERSION=v2`
- `LOG_LEVEL=info`
- `DB_HOST=postgres-service`
- `DB_PORT=5432`
- `DB_NAME=userdb`
- Plus all secret values

### Health Check Endpoint
Implement `/api/health` endpoint:

```javascript
app.get('/api/health', async (req, res) => {
  try {
    // Check database connection
    await db.query('SELECT 1');
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.API_VERSION,
      database: 'connected'
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});
```

## Testing the Deployment

### 1. Port Forward for Testing
```bash
# Forward API port
kubectl port-forward svc/user-api-service 8080:8080 -n user-api

# Test health endpoint
curl http://localhost:8080/api/health
```

### 2. Database Testing
```bash
# Connect to database
kubectl port-forward svc/postgres-service 5432:5432 -n user-api

# Connect with psql
psql -h localhost -U apiuser -d userdb
```

### 3. API Testing
```bash
# Test API endpoints
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"email":"<EMAIL>","password":"password123"}'

curl http://localhost:8080/api/users \
  -H "Authorization: Bearer your-jwt-token"
```

## Production Considerations

### Security
- Use strong passwords for database
- Rotate JWT secrets regularly
- Implement proper RBAC
- Use network policies for isolation

### Monitoring
```bash
# Monitor resource usage
kubectl top pods -n user-api

# Check logs
kubectl logs deployment/user-api -n user-api --tail=100
kubectl logs deployment/postgres -n user-api --tail=100
```

### Backup Strategy
```bash
# Create database backup
kubectl exec deployment/postgres -n user-api -- pg_dump -U apiuser userdb > backup.sql

# Restore from backup
kubectl exec -i deployment/postgres -n user-api -- psql -U apiuser userdb < backup.sql
```

### Scaling
```bash
# Scale API replicas
kubectl scale deployment user-api --replicas=5 -n user-api

# Monitor scaling
kubectl get hpa -n user-api  # if HPA is configured
```

## Troubleshooting

### Database Connection Issues
```bash
# Check database pod logs
kubectl logs deployment/postgres -n user-api

# Verify service connectivity
kubectl exec deployment/user-api -n user-api -- nc -zv postgres-service 5432

# Check secret values
kubectl get secret user-api-secrets -n user-api -o yaml
```

### API Pod Issues
```bash
# Check pod status
kubectl describe pod -l app=user-api -n user-api

# View application logs
kubectl logs deployment/user-api -n user-api --previous

# Check environment variables
kubectl exec deployment/user-api -n user-api -- env | grep -E "(DB_|JWT_|API_)"
```

### Performance Issues
```bash
# Check resource usage
kubectl top pods -n user-api

# Monitor database performance
kubectl exec deployment/postgres -n user-api -- psql -U apiuser -d userdb -c "
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;"
```
