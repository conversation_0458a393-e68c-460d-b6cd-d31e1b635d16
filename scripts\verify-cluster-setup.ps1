#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Verify cross-cluster ArgoCD setup

.DESCRIPTION
    This script verifies that ArgoCD is properly configured for cross-cluster deployment.
    It checks cluster connectivity, ArgoCD cluster registration, and configuration.

.PARAMETER Detailed
    Show detailed information about each check

.EXAMPLE
    ./verify-cluster-setup.ps1
    
.EXAMPLE
    ./verify-cluster-setup.ps1 -Detailed
#>

param(
    [switch]$Detailed
)

Write-Host "🔍 Verifying Cross-Cluster ArgoCD Setup" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

$errors = @()
$warnings = @()

# Function to check command availability
function Test-CommandExists {
    param([string]$Command)
    try {
        $null = Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to run command and capture output
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$Description
    )
    
    try {
        $output = Invoke-Expression $Command 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $Description" -ForegroundColor Green
            if ($Detailed) {
                Write-Host "   Command: $Command" -ForegroundColor Gray
                Write-Host "   Output: $output" -ForegroundColor Gray
            }
            return $output
        } else {
            Write-Host "❌ $Description" -ForegroundColor Red
            $script:errors += "$Description - Exit code: $LASTEXITCODE"
            if ($Detailed) {
                Write-Host "   Command: $Command" -ForegroundColor Gray
                Write-Host "   Error: $output" -ForegroundColor Red
            }
            return $null
        }
    } catch {
        Write-Host "❌ $Description" -ForegroundColor Red
        $script:errors += "$Description - Exception: $($_.Exception.Message)"
        if ($Detailed) {
            Write-Host "   Command: $Command" -ForegroundColor Gray
            Write-Host "   Exception: $($_.Exception.Message)" -ForegroundColor Red
        }
        return $null
    }
}

Write-Host "`n📋 Checking Prerequisites..." -ForegroundColor Cyan

# Check required tools
$tools = @(
    @{ Name = "kubectl"; Description = "Kubernetes CLI" },
    @{ Name = "argocd"; Description = "ArgoCD CLI" },
    @{ Name = "doctl"; Description = "DigitalOcean CLI" }
)

foreach ($tool in $tools) {
    if (Test-CommandExists $tool.Name) {
        Write-Host "✅ $($tool.Description) ($($tool.Name)) is available" -ForegroundColor Green
    } else {
        Write-Host "❌ $($tool.Description) ($($tool.Name)) is not available" -ForegroundColor Red
        $errors += "$($tool.Description) is required but not found"
    }
}

Write-Host "`n🔗 Checking Cluster Connectivity..." -ForegroundColor Cyan

# Check current kubectl context
$currentContext = Invoke-SafeCommand "kubectl config current-context" "Get current kubectl context"
if ($currentContext) {
    Write-Host "📍 Current kubectl context: $currentContext" -ForegroundColor White
}

# Check cluster info
$clusterInfo = Invoke-SafeCommand "kubectl cluster-info" "Verify cluster connectivity"

# Check ArgoCD namespace
$argoCDNamespace = Invoke-SafeCommand "kubectl get namespace argocd" "Check ArgoCD namespace"

Write-Host "`n🎯 Checking ArgoCD Configuration..." -ForegroundColor Cyan

# Check ArgoCD server status
$argoCDPods = Invoke-SafeCommand "kubectl get pods -n argocd -l app.kubernetes.io/name=argocd-server" "Check ArgoCD server pods"

# List ArgoCD clusters
$argoCDClusters = Invoke-SafeCommand "argocd cluster list" "List ArgoCD registered clusters"

if ($argoCDClusters) {
    Write-Host "`n📊 ArgoCD Cluster Registration:" -ForegroundColor Yellow
    
    # Check for expected clusters
    $expectedClusters = @(
        "https://kubernetes.default.svc",
        "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com"
    )
    
    foreach ($expectedCluster in $expectedClusters) {
        if ($argoCDClusters -match [regex]::Escape($expectedCluster)) {
            Write-Host "✅ Cluster registered: $expectedCluster" -ForegroundColor Green
        } else {
            Write-Host "❌ Cluster not registered: $expectedCluster" -ForegroundColor Red
            $errors += "Expected cluster not registered: $expectedCluster"
        }
    }
}

Write-Host "`n🔧 Checking Configuration Files..." -ForegroundColor Cyan

# Check if templates exist
$templateFiles = @(
    "templates/argocd/application.yaml",
    "templates/argocd/project.yaml"
)

foreach ($templateFile in $templateFiles) {
    if (Test-Path $templateFile) {
        Write-Host "✅ Template exists: $templateFile" -ForegroundColor Green
        
        # Check for CLUSTER_SERVER variable in templates
        $content = Get-Content $templateFile -Raw
        if ($content -match "CLUSTER_SERVER") {
            Write-Host "✅ Template contains CLUSTER_SERVER variable" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Template missing CLUSTER_SERVER variable" -ForegroundColor Yellow
            $warnings += "Template $templateFile missing CLUSTER_SERVER variable"
        }
    } else {
        Write-Host "❌ Template missing: $templateFile" -ForegroundColor Red
        $errors += "Required template file missing: $templateFile"
    }
}

# Check generation scripts
$generationScripts = @(
    "scripts/generate-manifests-cicd.py",
    "scripts/generate-manifests-cicd.ps1",
    "scripts/setup-external-cluster.ps1"
)

foreach ($script in $generationScripts) {
    if (Test-Path $script) {
        Write-Host "✅ Script exists: $script" -ForegroundColor Green
    } else {
        Write-Host "❌ Script missing: $script" -ForegroundColor Red
        $errors += "Required script missing: $script"
    }
}

Write-Host "`n📈 Summary" -ForegroundColor Cyan
Write-Host "==========" -ForegroundColor Cyan

if ($errors.Count -eq 0 -and $warnings.Count -eq 0) {
    Write-Host "🎉 All checks passed! Cross-cluster setup is ready." -ForegroundColor Green
} else {
    if ($errors.Count -gt 0) {
        Write-Host "`n❌ Errors found ($($errors.Count)):" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "   • $error" -ForegroundColor Red
        }
    }
    
    if ($warnings.Count -gt 0) {
        Write-Host "`n⚠️  Warnings ($($warnings.Count)):" -ForegroundColor Yellow
        foreach ($warning in $warnings) {
            Write-Host "   • $warning" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n📚 Next Steps:" -ForegroundColor Cyan
if ($errors.Count -gt 0) {
    Write-Host "1. Fix the errors listed above" -ForegroundColor White
    Write-Host "2. Run the setup script: ./scripts/setup-external-cluster.ps1" -ForegroundColor White
    Write-Host "3. Re-run this verification script" -ForegroundColor White
} else {
    Write-Host "1. If target cluster is not registered, run: ./scripts/setup-external-cluster.ps1" -ForegroundColor White
    Write-Host "2. Test deployment with: ./scripts/generate-manifests-cicd.ps1 -Environment dev" -ForegroundColor White
    Write-Host "3. Monitor ArgoCD applications: argocd app list" -ForegroundColor White
}

Write-Host "`n📖 For detailed setup instructions, see: docs/cross-cluster-setup.md" -ForegroundColor Cyan

exit $errors.Count
