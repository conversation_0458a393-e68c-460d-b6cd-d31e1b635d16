apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: {{PROJECT_ID}}
  namespace: argocd
  labels:
    app.kubernetes.io/name: {{PROJECT_ID}}
    app.kubernetes.io/part-of: {{PROJECT_ID}}
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: {{PROJECT_ID}}-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: {{PROJECT_ID}}/k8s
  destination:
    server: {{#if CLUSTER_SERVER}}{{CLUSTER_SERVER}}{{else}}https://kubernetes.default.svc{{/if}}
    namespace: {{NAMESPACE}}
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "{{APP_NAME}} - {{#eq APP_TYPE 'react-frontend'}}React Frontend Application{{else}}{{#eq APP_TYPE 'springboot-backend'}}Spring Boot Backend API{{else}}{{#eq APP_TYPE 'django-backend'}}Django Backend API{{else}}{{#eq APP_TYPE 'nest-backend'}}NestJS Backend API{{else}}{{APP_TYPE}} deployment{{/eq}}{{/eq}}{{/eq}}{{/eq}}"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "{{ENVIRONMENT}}"
  - name: Application Type
    value: "{{APP_TYPE}}"
  - name: Configuration
    value: "{{#eq APP_TYPE 'react-frontend'}}Static serving, build-time env vars{{else}}{{#eq APP_TYPE 'springboot-backend'}}Database integration, JWT auth, health checks{{else}}{{#eq APP_TYPE 'django-backend'}}Django ORM, REST framework, admin interface{{else}}{{#eq APP_TYPE 'nest-backend'}}TypeScript, decorators, dependency injection{{else}}Standard configuration{{/eq}}{{/eq}}{{/eq}}{{/eq}}"
