apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{PROJECT_ID}}
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: {{APP_TYPE}}
    version: v1.0.0
    environment: {{ENVIRONMENT}}
spec:
  replicas: {{REPLICAS}}
  selector:
    matchLabels:
      app: {{PROJECT_ID}}
      app.kubernetes.io/name: {{PROJECT_ID}}
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: {{PROJECT_ID}}
        app.kubernetes.io/name: {{PROJECT_ID}}
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: {{APP_TYPE}}
        version: v1.0.0
        environment: {{ENVIRONMENT}}
    spec:
      {{#eq APP_TYPE 'react-frontend'}}
      # React Frontend - No init containers needed (stateless)
      {{else}}
      {{#if ENABLE_DATABASE}}
      # Backend Applications - Managed Database connectivity check
      initContainers:
      - name: wait-for-managed-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h {{DB_HOST}} -p {{DB_PORT}} -U {{DB_USER}}; do
            echo "Waiting for managed PostgreSQL database to be ready..."
            sleep 2
          done
          echo "Managed PostgreSQL database is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_SSL_MODE
      {{/if}}
      {{/eq}}
      containers:
      - name: {{PROJECT_ID}}
        image: {{CONTAINER_IMAGE}}
        imagePullPolicy: Always
        ports:
        - containerPort: {{CONTAINER_PORT}}
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: {{PROJECT_ID}}-config
        {{#eq APP_TYPE 'react-frontend'}}
        # React Frontend - Volume mount for custom nginx configuration
        volumeMounts:
        - name: nginx-conf
          mountPath: /etc/nginx/conf.d/default.conf
          subPath: default.conf
        {{else}}
        # Backend Applications - Full secret environment variables
        env:
        {{#eq APP_TYPE 'springboot-backend'}}
        # Spring Boot specific database configuration
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_USER
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: SPRING_MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_USER
        - name: SPRING_MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_PASS
        - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_ID
        - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_SECRET
        {{else}}
        {{#eq APP_TYPE 'django-backend'}}
        # Django specific secret configuration
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DJANGO_SECRET_KEY
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: JWT_SECRET
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: EMAIL_HOST_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_USER
        - name: EMAIL_HOST_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_PASS
        - name: GOOGLE_OAUTH2_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_SECRET
        {{else}}
        {{#eq APP_TYPE 'nest-backend'}}
        # NestJS specific secret configuration
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: JWT_SECRET
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_SECRET
        {{else}}
        # Common backend secrets for other applications
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: JWT_SECRET
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: DB_PASSWORD
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{PROJECT_ID}}-secrets
              key: GOOGLE_CLIENT_SECRET
        {{/eq}}
        {{/eq}}
        {{/eq}}
        {{/eq}}
        # Health Checks - Application Type Specific
        {{#eq APP_TYPE 'react-frontend'}}
        # TCP health checks for React Frontend to avoid application-specific failures
        livenessProbe:
          tcpSocket:
            port: {{#eq CONTAINER_PORT '3000'}}3000{{else}}80{{/eq}}
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: {{#eq CONTAINER_PORT '3000'}}3000{{else}}80{{/eq}}
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        {{else}}
        {{#eq APP_TYPE 'springboot-backend'}}
        livenessProbe:
          tcpSocket:
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        {{else}}
        {{#eq APP_TYPE 'django-backend'}}
        livenessProbe:
           httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        {{else}}
        {{#eq APP_TYPE 'nest-backend'}}
        livenessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: {{HEALTH_CHECK_PATH}}
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        {{else}}
        # Default health checks for other application types
        livenessProbe:
          tcpSocket:
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          tcpSocket:
            port: {{CONTAINER_PORT}}
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        {{/eq}}
        {{/eq}}
        {{/eq}}
        {{/eq}}
        resources:
          requests:
            memory: "{{MEMORY_REQUEST}}"
            cpu: "{{CPU_REQUEST}}"
          limits:
            memory: "{{MEMORY_LIMIT}}"
            cpu: "{{CPU_LIMIT}}"
      {{#eq APP_TYPE 'react-frontend'}}
      # Volume for nginx configuration (React Frontend)
      volumes:
      - name: nginx-conf
        configMap:
          name: {{PROJECT_ID}}-nginx-config
      {{/eq}}
