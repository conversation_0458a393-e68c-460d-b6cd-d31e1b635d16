#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Complete Step 1: Install and configure ArgoCD CLI

.DESCRIPTION
    This script completes the ArgoCD CLI installation and setup for cross-cluster deployment.
    It downloads the latest ArgoCD CLI, installs it, and verifies the installation.

.EXAMPLE
    ./step1-install-argocd-cli.ps1
#>

Write-Host "🚀 Step 1: Installing and configuring ArgoCD CLI..." -ForegroundColor Yellow

# Check if ArgoCD CLI is already properly installed
$argoCdPath = "$env:USERPROFILE\bin\argocd.exe"
if (Test-Path $argoCdPath) {
    Write-Host "✅ ArgoCD CLI already installed at: $argoCdPath" -ForegroundColor Green
    
    # Test the installation
    Write-Host "🔍 Testing ArgoCD CLI..." -ForegroundColor Cyan
    try {
        $version = & $argoCdPath version --client 2>&1
        if ($version -match "argocd: v") {
            Write-Host "✅ ArgoCD CLI is working correctly" -ForegroundColor Green
            Write-Host "   Version: $($version | Select-String 'argocd: v')" -ForegroundColor White
        } else {
            throw "Version check failed"
        }
    } catch {
        Write-Host "❌ ArgoCD CLI test failed: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "🔄 Re-installing ArgoCD CLI..." -ForegroundColor Yellow
        Remove-Item $argoCdPath -Force -ErrorAction SilentlyContinue
    }
}

# Install ArgoCD CLI if not present or not working
if (-not (Test-Path $argoCdPath)) {
    Write-Host "📥 Downloading ArgoCD CLI..." -ForegroundColor Cyan
    
    # Create bin directory if it doesn't exist
    $binDir = "$env:USERPROFILE\bin"
    if (-not (Test-Path $binDir)) {
        New-Item -ItemType Directory -Path $binDir -Force | Out-Null
        Write-Host "📁 Created directory: $binDir" -ForegroundColor White
    }
    
    # Download latest ArgoCD CLI
    $version = "v2.12.3"  # Latest stable version
    $url = "https://github.com/argoproj/argo-cd/releases/download/$version/argocd-windows-amd64.exe"
    
    try {
        Write-Host "   Downloading from: $url" -ForegroundColor White
        Invoke-WebRequest -Uri $url -OutFile $argoCdPath -UseBasicParsing
        Write-Host "✅ Download completed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    # Test the installation
    Write-Host "🔍 Testing new installation..." -ForegroundColor Cyan
    try {
        $version = & $argoCdPath version --client 2>&1
        if ($version -match "argocd: v") {
            Write-Host "✅ ArgoCD CLI installed and working correctly" -ForegroundColor Green
            Write-Host "   Version: $($version | Select-String 'argocd: v')" -ForegroundColor White
        } else {
            throw "Version check failed"
        }
    } catch {
        Write-Host "❌ Installation test failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

# Update PATH for current session
$env:PATH += ";$env:USERPROFILE\bin"
Write-Host "🔧 Updated PATH for current session" -ForegroundColor Cyan

# Check if directory is in permanent PATH
Write-Host "🔍 Checking permanent PATH configuration..." -ForegroundColor Cyan
$currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
if ($currentPath -notlike "*$env:USERPROFILE\bin*") {
    Write-Host "🔧 Adding to permanent user PATH..." -ForegroundColor Yellow
    try {
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$env:USERPROFILE\bin", "User")
        Write-Host "✅ Added to permanent PATH" -ForegroundColor Green
        Write-Host "   Note: New terminals will automatically have access to 'argocd' command" -ForegroundColor White
    } catch {
        Write-Host "⚠️  Could not update permanent PATH: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "   You can manually add '$env:USERPROFILE\bin' to your PATH" -ForegroundColor White
    }
} else {
    Write-Host "✅ Already in permanent PATH" -ForegroundColor Green
}

# Clean up old problematic installation
Write-Host "🧹 Checking for old installations..." -ForegroundColor Cyan
if (Test-Path "C:\windows\system32\argocd.exe") {
    Write-Host "⚠️  Found old ArgoCD installation in system32" -ForegroundColor Yellow
    Write-Host "   This may cause conflicts. Consider removing it with administrator privileges." -ForegroundColor White
    Write-Host "   Command: Remove-Item 'C:\windows\system32\argocd.exe' -Force" -ForegroundColor White
}

# Final verification
Write-Host "`n🎉 Step 1 completed successfully!" -ForegroundColor Green
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "   ✅ ArgoCD CLI installed: $argoCdPath" -ForegroundColor White
Write-Host "   ✅ PATH updated for current session" -ForegroundColor White
Write-Host "   ✅ Installation tested and working" -ForegroundColor White

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "   1. Connect to your ArgoCD server (Step 2)" -ForegroundColor White
Write-Host "   2. Run: .\scripts\setup-external-cluster.ps1" -ForegroundColor White
Write-Host "   3. Verify cluster registration" -ForegroundColor White

Write-Host "`n💡 Usage:" -ForegroundColor Cyan
Write-Host "   Use full path: $argoCdPath" -ForegroundColor White
Write-Host "   Or in new terminals: argocd" -ForegroundColor White
