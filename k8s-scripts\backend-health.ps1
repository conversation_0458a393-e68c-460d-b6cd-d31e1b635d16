Write-Host "🏥 Backend Health Check" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

# Get current config
$CONFIG = kubectl get configmap ai-react-frontend-runtime-config -n ai-react-frontend-dev -o jsonpath='{.data.runtime-config\.json}'
$configObj = $CONFIG | ConvertFrom-Json
$CURRENT_BACKEND = $configObj.currentBackend
$CURRENT_URL = $configObj.backendUrl

Write-Host "Current Backend: $CURRENT_BACKEND" -ForegroundColor White
Write-Host "Current URL: $CURRENT_URL" -ForegroundColor White
Write-Host ""

# Test all backends
Write-Host "Testing all backends:" -ForegroundColor Yellow
Write-Host "--------------------" -ForegroundColor Yellow

# Spring
Write-Host -NoNewline "Spring (*************:8080): "
try {
    $response = Invoke-WebRequest -Uri "http://*************:8080/actuator/health" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Healthy" -ForegroundColor Green
    } else {
        Write-Host "❌ Down" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Down" -ForegroundColor Red
}

# Django  
Write-Host -NoNewline "Django (*************:8000): "
try {
    $response = Invoke-WebRequest -Uri "http://*************:8000/health" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Healthy" -ForegroundColor Green
    } else {
        Write-Host "❌ Down" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Down" -ForegroundColor Red
}

# NestJS
Write-Host -NoNewline "NestJS (**************:3000): "
try {
    $response = Invoke-WebRequest -Uri "http://**************:3000/health" -Method GET -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Healthy" -ForegroundColor Green
    } else {
        Write-Host "❌ Down" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Down" -ForegroundColor Red
}

Write-Host ""
Write-Host "Frontend URL: http://*************:3000" -ForegroundColor Cyan
Write-Host "Config API: http://*************:3000/api/config" -ForegroundColor Cyan
