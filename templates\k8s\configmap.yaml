apiVersion: v1
kind: ConfigMap
metadata:
  name: {{PROJECT_ID}}-config
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: config
    environment: {{ENVIRONMENT}}
    app-type: {{APP_TYPE}}
data:
  # Application Configuration
  NODE_ENV: "{{ENVIRONMENT}}"
  PORT: "{{CONTAINER_PORT}}"

  {{#eq APP_TYPE 'react-frontend'}}
  # React Frontend Configuration
  REACT_APP_API_URL: "{{API_URL}}"
  REACT_APP_ENVIRONMENT: "{{ENVIRONMENT}}"
  REACT_APP_VERSION: "{{APP_VERSION}}"
  REACT_APP_TITLE: "{{APP_NAME}}"
  PUBLIC_URL: "{{PUBLIC_URL}}"
  GENERATE_SOURCEMAP: "{{#eq ENVIRONMENT 'dev'}}true{{else}}false{{/eq}}"
  {{/eq}}
  {{#eq APP_TYPE 'springboot-backend'}}
  # Spring Boot Backend Configuration
  SPRING_PROFILES_ACTIVE: "{{ENVIRONMENT}}"
  SERVER_PORT: "{{CONTAINER_PORT}}"
  SPRING_APPLICATION_NAME: "{{PROJECT_ID}}"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "when-authorized"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"

  # JVM Configuration
  JAVA_OPTS: "-Xms{{JAVA_XMS}} -Xmx{{JAVA_XMX}} -XX:+UseG1GC -XX:+UseContainerSupport"

  # Application URLs
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"
  {{/eq}}
  {{#eq APP_TYPE 'django-backend'}}
  # Django Backend Configuration
  DJANGO_SETTINGS_MODULE: "{{PROJECT_ID}}.settings.{{ENVIRONMENT}}"
  DEBUG: "{{#eq ENVIRONMENT 'dev'}}True{{else}}False{{/eq}}"
  ALLOWED_HOSTS: "{{ALLOWED_HOSTS}}"
  SECRET_KEY: "{{DJANGO_SECRET_KEY}}"

  # Django Database Configuration (Managed DigitalOcean Database)
  DATABASE_URL: "postgresql://{{DB_USER}}:{{DB_PASSWORD}}@{{DB_HOST}}:{{DB_PORT}}/{{DB_NAME}}?sslmode={{DB_SSL_MODE}}"

  # Django Static Files
  STATIC_URL: "/static/"
  STATIC_ROOT: "/app/staticfiles/"
  MEDIA_URL: "/media/"
  MEDIA_ROOT: "/app/media/"

  # Django CORS Configuration
  CORS_ALLOWED_ORIGINS: "{{CORS_ORIGINS}}"
  CORS_ALLOW_CREDENTIALS: "true"

  # Django Application URLs
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"
  {{/eq}}
  {{#eq APP_TYPE 'nest-backend'}}
  # NestJS Backend Configuration
  NODE_ENV: "{{ENVIRONMENT}}"
  APP_PORT: "{{CONTAINER_PORT}}"

  # NestJS Database Configuration (Managed DigitalOcean Database)
  DATABASE_URL: "postgresql://{{DB_USER}}:{{DB_PASSWORD}}@{{DB_HOST}}:{{DB_PORT}}/{{DB_NAME}}?sslmode={{DB_SSL_MODE}}"
  DB_HOST: "{{DB_HOST}}"
  DB_PORT: "{{DB_PORT}}"
  DB_NAME: "{{DB_NAME}}"
  DB_USERNAME: "{{DB_USER}}"
  DB_SSL_MODE: "{{DB_SSL_MODE}}"

  # NestJS Application URLs
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"

  # NestJS CORS Configuration
  CORS_ORIGIN: "{{CORS_ORIGINS}}"
  CORS_CREDENTIALS: "true"
  {{/eq}}
  {{#eq APP_TYPE 'nodejs-backend'}}
  # Other Backend Application Configuration
  APP_URL: "{{APP_URL}}"
  API_URL: "{{API_URL}}"
  {{/eq}}

  {{#eq APP_TYPE 'springboot-backend'}}
  # Spring Boot Database Configuration (Managed DigitalOcean Database)
  SPRING_DATASOURCE_URL: "jdbc:postgresql://{{DB_HOST}}:{{DB_PORT}}/{{DB_NAME}}?sslmode={{DB_SSL_MODE}}"
  SPRING_DATASOURCE_DRIVER_CLASS_NAME: "org.postgresql.Driver"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "{{#eq ENVIRONMENT 'dev'}}update{{else}}validate{{/eq}}"
  SPRING_JPA_SHOW_SQL: "{{#eq ENVIRONMENT 'dev'}}true{{else}}false{{/eq}}"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "{{#eq ENVIRONMENT 'dev'}}true{{else}}false{{/eq}}"

  # Spring Boot Security & JWT
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_SCOPE: "{{OAUTH_SCOPES}}"

  # Spring Boot CORS Configuration
  CORS_ALLOWED_ORIGINS: "{{CORS_ORIGINS}}"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"

  # Spring Boot Mail Configuration
  SPRING_MAIL_HOST: "{{SMTP_HOST}}"
  SPRING_MAIL_PORT: "{{SMTP_PORT}}"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH: "true"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE: "true"
  {{/eq}}
  {{#eq APP_TYPE 'django-backend'}}
  # Django Security & JWT Configuration
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"
  JWT_ALGORITHM: "HS256"

  # Django SMTP Configuration
  EMAIL_BACKEND: "django.core.mail.backends.smtp.EmailBackend"
  EMAIL_HOST: "{{SMTP_HOST}}"
  EMAIL_PORT: "{{SMTP_PORT}}"
  EMAIL_USE_TLS: "true"
  EMAIL_FROM: "{{SMTP_FROM}}"

  # Django OAuth2 Configuration
  GOOGLE_OAUTH2_CLIENT_ID: "{{GOOGLE_CLIENT_ID}}"
  GOOGLE_OAUTH2_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  SOCIAL_AUTH_GOOGLE_OAUTH2_SCOPE: "{{OAUTH_SCOPES}}"
  {{/eq}}
  {{#eq APP_TYPE 'nest-backend'}}
  # NestJS Security & JWT Configuration
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"
  JWT_ALGORITHM: "HS256"

  # NestJS SMTP Configuration
  SMTP_HOST: "{{SMTP_HOST}}"
  SMTP_PORT: "{{SMTP_PORT}}"
  SMTP_FROM: "{{SMTP_FROM}}"
  SMTP_SECURE: "false"
  SMTP_TLS: "true"

  # NestJS OAuth2 Configuration
  GOOGLE_CLIENT_ID: "{{GOOGLE_CLIENT_ID}}"
  GOOGLE_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  OAUTH_SCOPES: "{{OAUTH_SCOPES}}"
  {{/eq}}
  {{#eq APP_TYPE 'nodejs-backend'}}
  # Other Backend Applications - Generic Configuration (Managed DigitalOcean Database)
  DB_HOST: "{{DB_HOST}}"
  DB_PORT: "{{DB_PORT}}"
  DB_NAME: "{{DB_NAME}}"
  DB_SSL_MODE: "{{DB_SSL_MODE}}"

  # JWT Configuration
  JWT_EXPIRATION: "{{JWT_EXPIRATION}}"

  # CORS Configuration
  CORS_ALLOWED_ORIGINS: "{{CORS_ORIGINS}}"
  CORS_ALLOWED_METHODS: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
  CORS_ALLOW_CREDENTIALS: "true"
  CORS_MAX_AGE: "3600"

  # SMTP Configuration
  SMTP_HOST: "{{SMTP_HOST}}"
  SMTP_PORT: "{{SMTP_PORT}}"
  SMTP_FROM: "{{SMTP_FROM}}"

  # OAuth2 Configuration
  GOOGLE_REDIRECT_URI: "{{GOOGLE_REDIRECT_URI}}"
  GOOGLE_SCOPE: "{{OAUTH_SCOPES}}"
  OAUTH2_AUTHORIZED_REDIRECT_URIS: "{{OAUTH_REDIRECT_URIS}}"
  {{/eq}}
