#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Setup external DOKS cluster for ArgoCD cross-cluster deployment

.DESCRIPTION
    This script configures ArgoCD to deploy applications to an external DOKS cluster.
    ArgoCD runs on the management cluster (158b6a47-3e7e-4dca-af0f-05a6e07115af)
    and deploys applications to the target cluster (6be4e15d-52f9-431d-84ec-ec8cad0dff2d).

.PARAMETER ClusterId
    Target DOKS cluster ID (default: 6be4e15d-52f9-431d-84ec-ec8cad0dff2d)

.PARAMETER ClusterName
    Name to register the cluster as in ArgoCD (default: doks-target-cluster)

.PARAMETER DryRun
    Show what would be done without making changes

.EXAMPLE
    ./setup-external-cluster.ps1

.EXAMPLE
    ./setup-external-cluster.ps1 -ClusterId "6be4e15d-52f9-431d-84ec-ec8cad0dff2d" -ClusterName "doks-target-cluster"
#>

param(
    [string]$ClusterId = "6be4e15d-52f9-431d-84ec-ec8cad0dff2d",
    [string]$ClusterName = "doks-target-cluster",
    [switch]$DryRun
)

Write-Host "🔧 Setting up external cluster for ArgoCD cross-cluster deployment..." -ForegroundColor Yellow
Write-Host "📋 Configuration:" -ForegroundColor Cyan
Write-Host "   Management Cluster (ArgoCD): 158b6a47-3e7e-4dca-af0f-05a6e07115af" -ForegroundColor White
Write-Host "   Target Cluster (Apps):       $ClusterId" -ForegroundColor White
Write-Host "   Cluster Name in ArgoCD:      $ClusterName" -ForegroundColor White

# Check if ArgoCD CLI is available
$argoCdPath = "$env:USERPROFILE\bin\argocd.exe"
if (-not (Test-Path $argoCdPath)) {
    # Fallback to system argocd
    if (-not (Get-Command "argocd" -ErrorAction SilentlyContinue)) {
        Write-Host "❌ ArgoCD CLI not found. Please install it first." -ForegroundColor Red
        Write-Host "   Install: https://argo-cd.readthedocs.io/en/stable/cli_installation/" -ForegroundColor Yellow
        exit 1
    }
    $argoCdPath = "argocd"
}
Write-Host "📍 Using ArgoCD CLI: $argoCdPath" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host "`n🔍 DRY RUN MODE - Commands that would be executed:" -ForegroundColor Yellow
    Write-Host "doctl kubernetes cluster kubeconfig save $ClusterId" -ForegroundColor White
    Write-Host "$argoCdPath cluster add do-$ClusterId --name $ClusterName" -ForegroundColor White
    Write-Host "$argoCdPath cluster list" -ForegroundColor White
    exit 0
}



# Check if doctl is available
if (-not (Get-Command "doctl" -ErrorAction SilentlyContinue)) {
    Write-Host "❌ doctl CLI not found. Please install it first." -ForegroundColor Red
    exit 1
}

# Save current kubeconfig context
$currentContext = kubectl config current-context
Write-Host "📝 Current kubectl context: $currentContext" -ForegroundColor Cyan

# Save kubeconfig for target cluster
Write-Host "`n📥 Getting kubeconfig for target cluster $ClusterId..." -ForegroundColor Cyan
try {
    doctl kubernetes cluster kubeconfig save $ClusterId
    Write-Host "✅ Successfully saved kubeconfig for target cluster" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to save kubeconfig for target cluster: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Switch back to ArgoCD management cluster context
Write-Host "`n🔄 Switching back to ArgoCD management cluster..." -ForegroundColor Cyan
kubectl config use-context $currentContext

# Add cluster to ArgoCD
Write-Host "`n🔗 Adding target cluster to ArgoCD..." -ForegroundColor Cyan
$targetContext = "do-$ClusterId"
try {
    & $argoCdPath cluster add $targetContext --name $ClusterName
    Write-Host "✅ Successfully added cluster to ArgoCD" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Cluster might already be registered. Checking cluster list..." -ForegroundColor Yellow
}

# Verify cluster addition
Write-Host "`n✅ Verifying cluster setup..." -ForegroundColor Green
& $argoCdPath cluster list

Write-Host "`n🎉 External cluster setup completed!" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Your ArgoCD applications will now deploy to cluster: $ClusterId" -ForegroundColor White
Write-Host "   2. Use environment 'dev' or 'staging' to deploy to the target cluster" -ForegroundColor White
Write-Host "   3. Use environment 'production' to deploy to the management cluster" -ForegroundColor White