#!/bin/bash

# DOKS Self-Hosted Runner Setup Script
# This script configures GitHub Actions self-hosted runners to use DigitalOcean Kubernetes (DOKS)

set -e

echo "🚀 Setting up DOKS configuration for GitHub Actions self-hosted runners..."

# Configuration
CLUSTER_ID="158b6a47-3e7e-4dca-af0f-05a6e07115af"
CLUSTER_NAME="do-blr1-k8s-1-33-1-do-1-blr1-1752649393416"
DOCTL_VERSION="1.104.0"

# Check if running as root or with sudo
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  Running as root. This script should be run as the runner user."
    echo "Please run this script as the user that runs the GitHub Actions runner."
    exit 1
fi

# Function to install doctl
install_doctl() {
    echo "📦 Installing doctl CLI..."
    
    # Download doctl
    cd /tmp
    wget -q "https://github.com/digitalocean/doctl/releases/download/v${DOCTL_VERSION}/doctl-${DOCTL_VERSION}-linux-amd64.tar.gz"
    tar xf "doctl-${DOCTL_VERSION}-linux-amd64.tar.gz"
    
    # Install doctl
    sudo mv doctl /usr/local/bin/
    sudo chmod +x /usr/local/bin/doctl
    
    # Cleanup
    rm -f "doctl-${DOCTL_VERSION}-linux-amd64.tar.gz"
    
    echo "✅ doctl installed successfully"
}

# Function to install kubectl
install_kubectl() {
    echo "📦 Installing kubectl..."
    
    # Download kubectl
    curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    chmod +x kubectl
    sudo mv kubectl /usr/local/bin/
    
    echo "✅ kubectl installed successfully"
}

# Check if doctl is installed
if ! command -v doctl >/dev/null 2>&1; then
    install_doctl
else
    echo "✅ doctl is already installed"
fi

# Check if kubectl is installed
if ! command -v kubectl >/dev/null 2>&1; then
    install_kubectl
else
    echo "✅ kubectl is already installed"
fi

# Check if DIGITALOCEAN_ACCESS_TOKEN is set
if [ -z "$DIGITALOCEAN_ACCESS_TOKEN" ]; then
    echo "❌ DIGITALOCEAN_ACCESS_TOKEN environment variable is not set"
    echo ""
    echo "🔧 To fix this, you need to:"
    echo "1. Create a DigitalOcean Personal Access Token:"
    echo "   - Go to https://cloud.digitalocean.com/account/api/tokens"
    echo "   - Click 'Generate New Token'"
    echo "   - Give it a name like 'GitOps-Runner'"
    echo "   - Select 'Read' and 'Write' scopes"
    echo "   - Copy the token"
    echo ""
    echo "2. Add it as a repository secret:"
    echo "   - Go to your GitHub repository settings"
    echo "   - Navigate to Secrets and variables > Actions"
    echo "   - Add a new repository secret named 'DIGITALOCEAN_ACCESS_TOKEN'"
    echo "   - Paste your DigitalOcean token as the value"
    echo ""
    echo "3. Update your workflow to use the token:"
    echo "   - Add 'DIGITALOCEAN_ACCESS_TOKEN: \${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}' to env section"
    echo ""
    exit 1
fi

echo "✅ DIGITALOCEAN_ACCESS_TOKEN is set"

# Authenticate with DigitalOcean
echo "🔐 Authenticating with DigitalOcean..."
doctl auth init --access-token "$DIGITALOCEAN_ACCESS_TOKEN"

# Configure kubectl for DOKS
echo "⚙️  Configuring kubectl for DOKS cluster..."
doctl kubernetes cluster kubeconfig save "$CLUSTER_ID"

# Verify cluster connectivity
echo "🔍 Testing cluster connectivity..."
if kubectl cluster-info >/dev/null 2>&1; then
    echo "✅ Successfully connected to DOKS cluster"
    kubectl cluster-info
else
    echo "❌ Failed to connect to DOKS cluster"
    exit 1
fi

# Check ArgoCD namespace
echo "🔍 Checking ArgoCD installation..."
if kubectl get namespace argocd >/dev/null 2>&1; then
    echo "✅ ArgoCD namespace found"
else
    echo "❌ ArgoCD namespace not found"
    echo "Please install ArgoCD on your DOKS cluster first"
    exit 1
fi

# Test ArgoCD connectivity
echo "🔍 Testing ArgoCD connectivity..."
if kubectl get applications -n argocd >/dev/null 2>&1; then
    echo "✅ ArgoCD is accessible"
else
    echo "⚠️  ArgoCD applications not accessible (this might be normal if no apps are deployed yet)"
fi

echo ""
echo "🎉 DOKS runner setup completed successfully!"
echo ""
echo "📋 Configuration Summary:"
echo "  • Cluster: $CLUSTER_NAME"
echo "  • Cluster ID: $CLUSTER_ID"
echo "  • doctl version: $(doctl version)"
echo "  • kubectl version: $(kubectl version --client --short 2>/dev/null || kubectl version --client)"
echo "  • Current context: $(kubectl config current-context)"
echo ""
echo "🔗 Next Steps:"
echo "  • Your self-hosted runner is now configured for DOKS"
echo "  • GitHub Actions workflows will use this DOKS cluster"
echo "  • Make sure DIGITALOCEAN_ACCESS_TOKEN is set in repository secrets"
echo "  • Test your GitOps workflows"
