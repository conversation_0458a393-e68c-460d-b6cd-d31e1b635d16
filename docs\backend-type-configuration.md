# Backend Type Configuration for React Frontend

## Overview

The AI React Frontend application now supports dynamic backend configuration through the `backend_type` parameter. This allows the frontend to connect to different backend services (Spring, Django, or NestJS) based on the deployment configuration.

## Supported Backend Types

| Backend Type | Service | Dev Environment URL | Port |
|--------------|---------|-------------------|------|
| `spring` | AI Spring Backend | `http://*************:8080` | 8080 |
| `django` | AI Django Backend | `http://***************:8000` | 8000 |
| `nest` | AI NestJS Backend | `http://*************:3000` | 3000 |

## Usage

### Repository Dispatch Events

When triggering deployment via repository dispatch, include the `backend_type` parameter in the payload:

```json
{
  "event_type": "deploy-to-argocd",
  "client_payload": {
    "app_name": "AI React Frontend",
    "project_id": "ai-react-frontend",
    "application_type": "react-frontend",
    "environment": "dev",
    "docker_image": "registry.digitalocean.com/doks-registry/ai-react-frontend",
    "docker_tag": "latest",
    "source_repo": "ChidhagniConsulting/ai-react-frontend",
    "source_branch": "main",
    "commit_sha": "abc123",
    "backend_type": "spring"
  }
}
```

### GitHub Issues

When using GitHub Issues for deployment, add the `Backend Type` field to your issue:

```markdown
**Backend Type**: spring
```

### Command Line

When using the manifest generation scripts directly:

```bash
# Python script
python scripts/generate-manifests-cicd.py \
  --app-name "AI React Frontend" \
  --project-id "ai-react-frontend" \
  --application-type "react-frontend" \
  --environment "dev" \
  --docker-image "registry.digitalocean.com/doks-registry/ai-react-frontend" \
  --docker-tag "latest" \
  --backend-type "django" \
  --output-dir "."

# PowerShell script
./scripts/generate-manifests-cicd.ps1 \
  -AppName "AI React Frontend" \
  -ProjectId "ai-react-frontend" \
  -AppType "react-frontend" \
  -Environment "dev" \
  -DockerImage "registry.digitalocean.com/doks-registry/ai-react-frontend" \
  -DockerTag "latest" \
  -BackendType "nest"
```

## Environment-Specific Behavior

### Development Environment
- Uses external IP addresses for direct connectivity
- Allows testing with different backend services
- Supports cross-origin requests

### Staging/Production Environments
- Uses internal Kubernetes service names
- Provides better security and performance
- Follows service mesh patterns

## Default Behavior

- If `backend_type` is not specified for react-frontend applications, defaults to `spring`
- If `container_port` is not specified for react-frontend applications, defaults to `3000`
- Non-react-frontend applications are not affected by backend_type configuration

## Configuration Examples

### Spring Backend Configuration
```yaml
# Generated ConfigMap
data:
  REACT_APP_API_URL: "http://*************:8080"  # Dev
  # or "http://ai-spring-backend-service:8080"     # Staging/Prod
```

### Django Backend Configuration
```yaml
# Generated ConfigMap
data:
  REACT_APP_API_URL: "http://***************:8000"  # Dev
  # or "http://ai-django-backend-service:8000"      # Staging/Prod
```

### NestJS Backend Configuration
```yaml
# Generated ConfigMap
data:
  REACT_APP_API_URL: "http://*************:3000"  # Dev
  # or "http://ai-nest-backend-service:3000"       # Staging/Prod
```

## Testing

Use the provided test script to verify backend type functionality:

```bash
./test-backend-type-dispatch.sh
```

This script will:
1. Generate manifests for each backend type
2. Verify the correct API URLs are configured
3. Display sample dispatch event payloads

## Troubleshooting

### Common Issues

1. **Wrong API URL in ConfigMap**
   - Verify the `backend_type` parameter is correctly specified
   - Check that the backend service is running and accessible

2. **CORS Errors**
   - Ensure the backend service allows requests from the frontend domain
   - Verify the API URL format is correct

3. **Connection Refused**
   - Check that the external IP addresses are current
   - Verify the backend services are healthy

### Validation

To verify the configuration is working:

1. Check the generated ConfigMap:
   ```bash
   kubectl get configmap <project-id>-config -o yaml
   ```

2. Verify the frontend can reach the backend:
   ```bash
   kubectl exec -it <frontend-pod> -- curl $REACT_APP_API_URL/health
   ```

## Migration Guide

### Existing Deployments

Existing AI React Frontend deployments will continue to work with the default Spring backend configuration. To switch to a different backend:

1. Update your deployment configuration to include `backend_type`
2. Redeploy the application
3. Verify the new API URL in the ConfigMap

### Backward Compatibility

- All existing functionality remains unchanged
- Default behavior maintains Spring backend connectivity
- No breaking changes to existing deployments
