# Test DOKS Connection Script
# This script tests the current DOKS configuration and connectivity

Write-Host "🔍 Testing DOKS Connection and Configuration..." -ForegroundColor Green
Write-Host ""

# Test kubectl availability
Write-Host "1. Testing kubectl availability..." -ForegroundColor Yellow
try {
    $kubectlVersion = kubectl version --client --short 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ kubectl is available: $kubectlVersion" -ForegroundColor Green
    } else {
        throw "kubectl command failed"
    }
} catch {
    Write-Host "   ❌ kubectl is not available or not in PATH" -ForegroundColor Red
    exit 1
}

# Test current context
Write-Host "2. Testing current kubectl context..." -ForegroundColor Yellow
try {
    $currentContext = kubectl config current-context 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ Current context: $currentContext" -ForegroundColor Green

        if ($currentContext -like "*do-blr1-k8s*") {
            Write-Host "   ✅ Context appears to be DOKS cluster" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  Context does not appear to be DOKS cluster" -ForegroundColor Yellow
        }
    } else {
        throw "kubectl config command failed"
    }
} catch {
    Write-Host "   ❌ Failed to get current context" -ForegroundColor Red
    exit 1
}

# Test cluster connectivity
Write-Host "3. Testing cluster connectivity..." -ForegroundColor Yellow
try {
    $clusterInfo = kubectl cluster-info 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ Cluster is accessible" -ForegroundColor Green
        Write-Host "   📋 Cluster info:" -ForegroundColor Cyan
        $clusterInfo | ForEach-Object { Write-Host "      $_" -ForegroundColor Gray }
    } else {
        throw "kubectl cluster-info failed"
    }
} catch {
    Write-Host "   ❌ Cluster is not accessible" -ForegroundColor Red
    Write-Host "   💡 This might be the issue causing your GitHub Actions to fail" -ForegroundColor Yellow
    exit 1
}

# Test ArgoCD namespace
Write-Host "4. Testing ArgoCD namespace..." -ForegroundColor Yellow
try {
    $argoCDNamespace = kubectl get namespace argocd -o name 2>$null
    Write-Host "   ✅ ArgoCD namespace exists: $argoCDNamespace" -ForegroundColor Green
} catch {
    Write-Host "   ❌ ArgoCD namespace not found" -ForegroundColor Red
    Write-Host "   💡 You may need to install ArgoCD on your DOKS cluster" -ForegroundColor Yellow
}

# Test ArgoCD applications
Write-Host "5. Testing ArgoCD applications..." -ForegroundColor Yellow
try {
    $argoApps = kubectl get applications -n argocd 2>$null
    if ($argoApps) {
        Write-Host "   ✅ ArgoCD applications are accessible" -ForegroundColor Green
        Write-Host "   📋 Current applications:" -ForegroundColor Cyan
        $argoApps | ForEach-Object { Write-Host "      $_" -ForegroundColor Gray }
    } else {
        Write-Host "   ℹ️  No ArgoCD applications found (this is normal for new setups)" -ForegroundColor Blue
    }
} catch {
    Write-Host "   ⚠️  ArgoCD applications not accessible" -ForegroundColor Yellow
    Write-Host "   💡 This might be normal if ArgoCD is still starting up" -ForegroundColor Yellow
}

# Test doctl availability
Write-Host "6. Testing doctl availability..." -ForegroundColor Yellow
try {
    $doctlVersion = doctl version 2>$null
    Write-Host "   ✅ doctl is available: $doctlVersion" -ForegroundColor Green
} catch {
    Write-Host "   ❌ doctl is not available" -ForegroundColor Red
    Write-Host "   💡 You may need to install doctl for DOKS management" -ForegroundColor Yellow
}

# Check environment variables
Write-Host "7. Checking environment variables..." -ForegroundColor Yellow
if ($env:DIGITALOCEAN_ACCESS_TOKEN) {
    $tokenLength = $env:DIGITALOCEAN_ACCESS_TOKEN.Length
    Write-Host "   ✅ DIGITALOCEAN_ACCESS_TOKEN is set (length: $tokenLength)" -ForegroundColor Green
} else {
    Write-Host "   ⚠️  DIGITALOCEAN_ACCESS_TOKEN is not set" -ForegroundColor Yellow
    Write-Host "   💡 This is needed for GitHub Actions to authenticate with DOKS" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Summary:" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan

$issues = @()
$successes = @()

# Collect results
try {
    kubectl cluster-info >$null 2>&1
    $successes += "✅ Local kubectl can connect to DOKS cluster"
} catch {
    $issues += "❌ Local kubectl cannot connect to cluster"
}

try {
    kubectl get namespace argocd >$null 2>&1
    $successes += "✅ ArgoCD is installed on the cluster"
} catch {
    $issues += "❌ ArgoCD is not installed or not accessible"
}

if ($env:DIGITALOCEAN_ACCESS_TOKEN) {
    $successes += "✅ DIGITALOCEAN_ACCESS_TOKEN is set locally"
} else {
    $issues += "❌ DIGITALOCEAN_ACCESS_TOKEN is not set"
}

# Display results
if ($successes.Count -gt 0) {
    Write-Host ""
    Write-Host "✅ Working correctly:" -ForegroundColor Green
    $successes | ForEach-Object { Write-Host "   $_" -ForegroundColor Green }
}

if ($issues.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ Issues found:" -ForegroundColor Red
    $issues | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
    
    Write-Host ""
    Write-Host "🔧 Next steps to fix GitHub Actions:" -ForegroundColor Yellow
    Write-Host "1. Add DIGITALOCEAN_ACCESS_TOKEN as a GitHub repository secret" -ForegroundColor White
    Write-Host "2. Run the DOKS runner setup script on your self-hosted runners" -ForegroundColor White
    Write-Host "3. Ensure ENABLE_AUTO_DEPLOY repository variable is set to 'true'" -ForegroundColor White
    Write-Host "4. Test the updated workflow" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "🎉 Everything looks good! Your DOKS setup should work with GitHub Actions." -ForegroundColor Green
    Write-Host "   Make sure to run the setup script on your self-hosted runners." -ForegroundColor White
}

Write-Host ""
