# Automated ArgoCD Deployment Setup

This guide explains how to configure automated kubectl deployment for your GitOps automation system.

## Overview

The automated deployment system extends your existing GitOps workflow to automatically execute `kubectl apply` commands when new project folders are created. This eliminates the manual step of running kubectl commands after manifest generation.

## Features

- ✅ **Automatic Detection**: Detects when new project folders are created
- ✅ **Validation**: Validates ArgoCD manifests before deployment
- ✅ **Error Handling**: Graceful fallback to manual deployment if automation fails
- ✅ **Monitoring**: Waits for ArgoCD application sync and provides status updates
- ✅ **Flexible Configuration**: Can be enabled/disabled via repository variables

## Configuration Options

### Option 1: GitHub Actions with Self-Hosted Runners (Recommended)

This is the recommended approach for production environments where you have control over the runner environment.

#### Prerequisites

1. **Self-hosted GitHub Actions runner** with kubectl access to your Kubernetes cluster
2. **ArgoCD installed** in your Kubernetes cluster
3. **Proper RBAC permissions** for the service account used by kubectl

#### Setup Steps

1. **Configure Repository Variables**
   
   In your GitHub repository, go to Settings → Secrets and variables → Actions → Variables tab:
   
   ```
   Variable Name: ENABLE_AUTO_DEPLOY
   Variable Value: true
   ```

2. **Setup Self-Hosted Runner with kubectl**
   
   On your self-hosted runner machine:
   
   ```bash
   # Install kubectl (if not already installed)
   curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
   sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
   
   # Configure kubectl to access your cluster
   # Option A: Copy kubeconfig
   mkdir -p ~/.kube
   cp /path/to/your/kubeconfig ~/.kube/config
   
   # Option B: Use service account token (recommended for production)
   kubectl config set-cluster your-cluster --server=https://your-k8s-api-server
   kubectl config set-credentials github-actions --token=your-service-account-token
   kubectl config set-context github-actions --cluster=your-cluster --user=github-actions
   kubectl config use-context github-actions
   
   # Test connectivity
   kubectl cluster-info
   kubectl get namespaces
   ```

3. **Create Service Account for GitHub Actions (Recommended)**
   
   ```yaml
   # github-actions-rbac.yaml
   apiVersion: v1
   kind: ServiceAccount
   metadata:
     name: github-actions
     namespace: argocd
   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: ClusterRole
   metadata:
     name: github-actions-argocd
   rules:
   - apiGroups: ["argoproj.io"]
     resources: ["applications", "appprojects"]
     verbs: ["get", "list", "create", "update", "patch", "delete"]
   - apiGroups: [""]
     resources: ["namespaces"]
     verbs: ["get", "list", "create"]
   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: ClusterRoleBinding
   metadata:
     name: github-actions-argocd
   roleRef:
     apiGroup: rbac.authorization.k8s.io
     kind: ClusterRole
     name: github-actions-argocd
   subjects:
   - kind: ServiceAccount
     name: github-actions
     namespace: argocd
   ```
   
   Apply the RBAC configuration:
   ```bash
   kubectl apply -f github-actions-rbac.yaml
   ```

### Option 2: GitHub Actions with Hosted Runners + Secrets

If you prefer to use GitHub-hosted runners, you'll need to configure cluster access via secrets.

#### Setup Steps

1. **Configure Repository Secrets**
   
   In your GitHub repository, go to Settings → Secrets and variables → Actions → Secrets tab:
   
   ```
   Secret Name: KUBE_CONFIG
   Secret Value: <base64-encoded kubeconfig file>
   
   Secret Name: KUBE_SERVER
   Secret Value: https://your-k8s-api-server
   
   Secret Name: KUBE_TOKEN
   Secret Value: <service-account-token>
   ```

2. **Configure Repository Variables**
   
   ```
   Variable Name: ENABLE_AUTO_DEPLOY
   Variable Value: true
   ```

3. **Update GitHub Actions Workflow**
   
   The workflow will automatically detect and use these secrets when available.

### Option 3: Manual Deployment (Default)

If automated deployment is not configured or fails, the system falls back to manual deployment with clear instructions provided in the GitHub issue comments.

## Usage

### Automatic Deployment

When `ENABLE_AUTO_DEPLOY=true` and kubectl is properly configured:

1. Create a GitHub issue using the app-deployment template
2. The workflow will:
   - Generate manifests
   - Automatically deploy to ArgoCD
   - Provide status updates in the issue comments
   - Wait for application sync (with timeout)

### Manual Deployment

When automatic deployment is disabled or fails:

1. The workflow generates manifests as usual
2. Issue comments provide manual deployment commands:
   ```bash
   kubectl apply -f {project-name}/argocd/project.yaml
   kubectl apply -f {project-name}/argocd/application.yaml
   ```

## Monitoring and Troubleshooting

### Check Deployment Status

```bash
# Check ArgoCD applications
kubectl get applications -n argocd

# Check application details
kubectl describe application {project-name} -n argocd

# Check application pods
kubectl get pods -n {project-name}

# View application logs
kubectl logs -n {project-name} -l app={project-name}
```

### Common Issues

1. **kubectl not available**
   - Ensure kubectl is installed on the runner
   - Check PATH configuration

2. **Cluster not accessible**
   - Verify kubeconfig or service account token
   - Check network connectivity to Kubernetes API server
   - Verify RBAC permissions

3. **ArgoCD application fails to sync**
   - Check ArgoCD server status
   - Verify repository access from ArgoCD
   - Check application configuration

### Logs and Debugging

- **GitHub Actions logs**: Check the workflow run logs for detailed error information
- **ArgoCD UI**: Monitor application status in the ArgoCD dashboard
- **Kubernetes events**: `kubectl get events -n {project-name}`

## Security Considerations

1. **Use service accounts** instead of user credentials for production
2. **Limit RBAC permissions** to only what's needed for ArgoCD operations
3. **Rotate tokens regularly** and use short-lived credentials when possible
4. **Monitor access logs** for unauthorized kubectl usage
5. **Use secrets** for sensitive configuration data

## Advanced Configuration

### Custom Deployment Script

You can customize the deployment behavior by modifying `scripts/deploy-argocd-auto.ps1`:

```powershell
# Example: Add custom validation
./deploy-argocd-auto.ps1 -ProjectName "my-app" -Validate -WaitForSync -SyncTimeoutSeconds 600
```

### Environment-Specific Configuration

Configure different deployment behaviors for different environments by using GitHub environments and environment-specific variables.

## Support

For issues with the automated deployment system:

1. Check the [troubleshooting guide](TROUBLESHOOTING.md)
2. Review GitHub Actions workflow logs
3. Verify kubectl and cluster connectivity
4. Check ArgoCD application status

The system is designed to be resilient - if automated deployment fails, it will always fall back to providing manual deployment instructions.

## Quick Start

### 1. Enable Automated Deployment

Add this repository variable in GitHub:
```
ENABLE_AUTO_DEPLOY=true
```

### 2. Choose Your Setup Method

**Option A: Self-Hosted Runner (Recommended)**
```bash
# Setup kubectl authentication
./scripts/setup-kubectl-auth.ps1 -Mode service-account

# Test the setup
./scripts/setup-kubectl-auth.ps1 -Mode validate
```

**Option B: GitHub-Hosted Runner with Secrets**
```bash
# Generate kubeconfig secret
./scripts/setup-kubectl-auth.ps1 -Mode kubeconfig

# Add the generated KUBE_CONFIG secret to GitHub
```

**Option C: Fallback Automation (Local/Server)**
```bash
# Setup systemd service (Linux)
./scripts/setup-fallback-automation.ps1 -Mode systemd

# Or setup Windows service
./scripts/setup-fallback-automation.ps1 -Mode windows-service
```

### 3. Test the System

```bash
# Run comprehensive tests
./tests/test-automated-deployment.ps1 -TestMode all -DryRun

# Test just the scripts
./tests/test-automated-deployment.ps1 -TestMode scripts-only
```

### 4. Create a Test Application

Create a GitHub issue using the app-deployment template and watch the automation work!

## Troubleshooting

### Common Issues and Solutions

| Issue | Solution |
|-------|----------|
| `kubectl not available` | Install kubectl: `curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"` |
| `Cluster not accessible` | Check kubeconfig: `kubectl cluster-info` |
| `ArgoCD CRDs not found` | Install ArgoCD: `kubectl create namespace argocd && kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml` |
| `Permission denied` | Check RBAC: `kubectl auth can-i create applications.argoproj.io` |
| `GitHub Actions fails` | Check secrets and variables in repository settings |

### Debug Commands

```bash
# Check deployment script
./scripts/deploy-argocd-auto.ps1 -ProjectName test-app -DryRun -Validate

# Check watcher script
./scripts/watch-and-deploy.ps1 -RunOnce -DryRun

# Check kubectl setup
./scripts/setup-kubectl-auth.ps1 -Mode validate

# Run tests
./tests/test-automated-deployment.ps1 -TestMode all -DryRun
```
