apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-spring-backend
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-spring-backend
    app.kubernetes.io/part-of: ai-spring-backend
    environment: dev
    app-type: springboot-backend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-spring-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: ai-spring-backend/k8s
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-spring-backend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "ai-spring-backend - Spring Boot Backend API"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "springboot-backend"
  - name: Configuration
    value: "Database integration, JWT auth, health checks"
