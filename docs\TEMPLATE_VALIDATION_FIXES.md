# Template Validation Fixes

## Overview

This document describes the comprehensive fixes implemented to resolve Handlebars template processing errors that were causing ArgoCD deployment failures due to invalid YAML syntax.

## Issues Identified

### 1. Unprocessed Handlebars Syntax
- Generated YAML files contained unprocessed template syntax like `{{#if DB_HOST}}`, `{{else}}`, `{{/eq}}`
- This made the YAML files syntactically invalid
- ArgoCD deployment failed during YAML validation step

### 2. Missing Template Variables
- Templates referenced undefined variables like `{{STORAGE_SIZE}}`, `{{JWT_SECRET_B64}}`
- Simple variable replacement couldn't handle complex conditional logic
- Nested conditionals were not processed correctly

### 3. Inadequate Template Processing
- The original `replace_template_variables()` function only handled basic variable replacement
- Complex Handlebars syntax like `{{#eq ENVIRONMENT 'dev'}}` was not supported
- No validation of template syntax before processing

## Solutions Implemented

### 1. Enhanced Handlebars Processing

#### Added Comprehensive Conditional Processing
```python
def process_handlebars_conditionals(content, variables):
    """Process Handlebars-style conditional blocks"""
    # Handles nested conditionals by processing from innermost to outermost
    # Supports both #eq and #if conditionals
    # Processes up to 10 levels of nesting to prevent infinite loops
```

#### Equality Comparisons Support
```python
def process_eq_conditionals(content, variables):
    """Process {{#eq VARIABLE 'value'}}...{{else}}...{{/eq}} conditionals"""
    # Supports both with and without else blocks
    # Handles string comparisons with proper quoting
```

#### If Conditionals Support
```python
def process_if_conditionals(content, variables):
    """Process {{#if VARIABLE}}...{{else}}...{{/if}} conditionals"""
    # Supports truthy/falsy evaluation
    # Handles both with and without else blocks
```

### 2. Complete Variable Definitions

#### Added All Missing Variables
```python
variables = {
    # Basic project variables
    "PROJECT_ID", "NAMESPACE", "ENVIRONMENT", "APP_TYPE", "APP_NAME",
    
    # Storage configuration
    "STORAGE_SIZE": "10Gi",
    "PVC_SIZE": "5Gi",
    
    # Base64 encoded secret values
    "JWT_SECRET_B64": encode_base64("supersecretkey"),
    "DB_PASSWORD_B64": encode_base64("password"),
    "DB_USER_B64": encode_base64("postgres"),
    
    # Application configuration with defaults
    "APP_URL": "",  # Uses template defaults
    "API_URL": "",  # Uses template defaults
    
    # Feature flags
    "ENABLE_INGRESS": "false",
    "ENABLE_PVC": "false",
    "ENABLE_DATABASE": "true"
}
```

### 3. Template Validation System

#### Pre-Processing Validation
```python
def validate_template_file(template_file, variables):
    """Comprehensive template file validation"""
    # Syntax validation for unmatched blocks
    # Variable validation for undefined references
    # YAML structure validation
```

#### Syntax Error Detection
- Detects unmatched `{{#if}}` and `{{/if}}` blocks
- Identifies malformed conditional syntax
- Warns about complex nested conditionals
- Validates YAML structure expectations

#### Variable Validation
- Extracts all template variables from content
- Checks against defined variables dictionary
- Reports missing or undefined variables
- Suggests fixes for common issues

### 4. Standalone Validation Script

Created `scripts/validate-templates.py` for independent template validation:

```bash
python scripts/validate-templates.py
```

Features:
- Validates all template files in `templates/` directory
- Reports syntax errors and warnings
- Checks for unknown template variables
- Provides detailed validation summary

## Template Patterns Supported

### 1. Simple Variable Replacement
```yaml
name: {{PROJECT_ID}}
namespace: {{NAMESPACE}}
```

### 2. If Conditionals
```yaml
{{#if ENABLE_DATABASE}}
initContainers:
- name: wait-for-postgres
{{/if}}
```

### 3. If-Else Conditionals
```yaml
path: {{#if HEALTH_CHECK_PATH}}{{HEALTH_CHECK_PATH}}{{else}}/api/v1/email/status{{/if}}
```

### 4. Equality Comparisons
```yaml
DB_HOST: "{{#eq ENVIRONMENT 'dev'}}localhost{{else}}{{PROJECT_ID}}-postgres.{{NAMESPACE}}{{/eq}}"
```

### 5. Nested Conditionals
```yaml
DB_HOST: "{{#if DB_HOST}}{{DB_HOST}}{{else}}{{#eq ENVIRONMENT 'dev'}}localhost{{else}}{{PROJECT_ID}}-postgres.production{{/eq}}{{/if}}"
```

## Validation Integration

### In CI/CD Pipeline
The enhanced `generate-manifests-cicd.py` now includes:

1. **Pre-processing validation** - Validates all templates before processing
2. **Variable completeness check** - Ensures all required variables are defined
3. **Syntax validation** - Detects template syntax errors early
4. **Post-processing validation** - Validates generated YAML files

### Manual Validation
Use the standalone validation script:

```bash
# Validate all templates
python scripts/validate-templates.py

# Validate specific generated manifests
python scripts/validate-yaml.py ai-react-frontend/
```

## Benefits

### 1. Robust Template Processing
- All Handlebars syntax patterns are properly processed
- No unprocessed template syntax in generated files
- Consistent handling of conditional logic

### 2. Early Error Detection
- Template syntax errors caught before deployment
- Missing variables identified during validation
- Clear error messages for troubleshooting

### 3. Reliable ArgoCD Deployments
- All generated YAML files are syntactically valid
- ArgoCD can successfully parse and apply manifests
- Deployment failures due to template issues eliminated

### 4. Maintainable System
- Comprehensive validation prevents regression
- Clear documentation of supported patterns
- Easy to extend for new template features

## Usage Examples

### Generate and Validate Manifests
```bash
# Generate manifests with validation
python scripts/generate-manifests-cicd.py \
  --app-name "My App" \
  --project-id "my-app" \
  --environment "dev" \
  --docker-image "myrepo/app" \
  --docker-tag "latest"

# Validate generated files
python scripts/validate-yaml.py my-app/
```

### Validate Templates Only
```bash
# Check all templates for issues
python scripts/validate-templates.py
```

## Future Enhancements

1. **Additional Handlebars Helpers** - Support for more complex template logic
2. **Custom Variable Validation** - Project-specific variable requirements
3. **Template Linting** - Style and best practice validation
4. **IDE Integration** - Real-time template validation in editors

## Troubleshooting

### Common Issues

1. **Unmatched Conditional Blocks**
   - Ensure every `{{#if}}` has a matching `{{/if}}`
   - Check for typos in conditional syntax

2. **Missing Variables**
   - Add missing variables to the variables dictionary
   - Use empty strings for optional variables with template defaults

3. **Nested Conditional Complexity**
   - Limit nesting depth to avoid processing issues
   - Consider simplifying complex conditional logic

### Validation Commands

```bash
# Full validation pipeline
python scripts/validate-templates.py
python scripts/generate-manifests-cicd.py [args]
python scripts/validate-yaml.py [output-dir]
```
