# Simple DOKS Connection Test
Write-Host "🔍 Testing DOKS Connection..." -ForegroundColor Green

# Test kubectl
Write-Host "1. Testing kubectl..." -ForegroundColor Yellow
kubectl version --client --short
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ kubectl is working" -ForegroundColor Green
} else {
    Write-Host "   ❌ kubectl failed" -ForegroundColor Red
}

# Test current context
Write-Host "2. Testing current context..." -ForegroundColor Yellow
$context = kubectl config current-context
Write-Host "   Current context: $context" -ForegroundColor White

# Test cluster connectivity
Write-Host "3. Testing cluster connectivity..." -ForegroundColor Yellow
kubectl cluster-info
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ Cluster is accessible" -ForegroundColor Green
} else {
    Write-Host "   ❌ Cluster is not accessible" -ForegroundColor Red
}

# Test ArgoCD
Write-Host "4. Testing ArgoCD..." -ForegroundColor Yellow
kubectl get namespace argocd
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ ArgoCD namespace exists" -ForegroundColor Green
} else {
    Write-Host "   ❌ ArgoCD namespace not found" -ForegroundColor Red
}

# Check environment
Write-Host "5. Checking environment..." -ForegroundColor Yellow
if ($env:DIGITALOCEAN_ACCESS_TOKEN) {
    Write-Host "   ✅ DIGITALOCEAN_ACCESS_TOKEN is set" -ForegroundColor Green
} else {
    Write-Host "   ⚠️  DIGITALOCEAN_ACCESS_TOKEN is not set" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Summary: Your local setup appears to be working correctly." -ForegroundColor Cyan
Write-Host "   The issue is likely that your GitHub Actions runners need DOKS configuration." -ForegroundColor White
Write-Host ""
Write-Host "🔧 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Add DIGITALOCEAN_ACCESS_TOKEN as a GitHub repository secret" -ForegroundColor White
Write-Host "   2. The updated workflow will configure DOKS automatically" -ForegroundColor White
Write-Host "   3. Test your GitOps workflow again" -ForegroundColor White
