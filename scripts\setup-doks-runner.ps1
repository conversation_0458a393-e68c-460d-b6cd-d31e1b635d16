# DOKS Self-Hosted Runner Setup Script (PowerShell)
# This script configures GitHub Actions self-hosted runners to use DigitalOcean Kubernetes (DOKS)

param(
    [string]$AccessToken = $env:DIGITALOCEAN_ACCESS_TOKEN
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Setting up DOKS configuration for GitHub Actions self-hosted runners..." -ForegroundColor Green

# Configuration
$ClusterId = "158b6a47-3e7e-4dca-af0f-05a6e07115af"
$ClusterName = "do-blr1-k8s-1-33-1-do-1-blr1-1752649393416"
$DoctlVersion = "1.104.0"

# Function to install doctl
function Install-Doctl {
    Write-Host "📦 Installing doctl CLI..." -ForegroundColor Yellow
    
    $tempDir = $env:TEMP
    $doctlZip = "$tempDir\doctl-$DoctlVersion-windows-amd64.zip"
    $doctlUrl = "https://github.com/digitalocean/doctl/releases/download/v$DoctlVersion/doctl-$DoctlVersion-windows-amd64.zip"
    
    # Download doctl
    Invoke-WebRequest -Uri $doctlUrl -OutFile $doctlZip -UseBasicParsing
    
    # Extract doctl
    Expand-Archive -Path $doctlZip -DestinationPath $tempDir -Force
    
    # Move to Program Files
    $programFiles = $env:ProgramFiles
    $doctlDir = "$programFiles\doctl"
    if (!(Test-Path $doctlDir)) {
        New-Item -ItemType Directory -Path $doctlDir -Force | Out-Null
    }
    
    Copy-Item "$tempDir\doctl.exe" "$doctlDir\doctl.exe" -Force
    
    # Add to PATH if not already there
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    if ($currentPath -notlike "*$doctlDir*") {
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$doctlDir", "Machine")
        $env:PATH = "$env:PATH;$doctlDir"
    }
    
    # Cleanup
    Remove-Item $doctlZip -Force -ErrorAction SilentlyContinue
    Remove-Item "$tempDir\doctl.exe" -Force -ErrorAction SilentlyContinue
    
    Write-Host "✅ doctl installed successfully" -ForegroundColor Green
}

# Function to install kubectl
function Install-Kubectl {
    Write-Host "📦 Installing kubectl..." -ForegroundColor Yellow
    
    $tempDir = $env:TEMP
    $kubectlExe = "$tempDir\kubectl.exe"
    $kubectlUrl = "https://dl.k8s.io/release/v1.28.0/bin/windows/amd64/kubectl.exe"
    
    # Download kubectl
    Invoke-WebRequest -Uri $kubectlUrl -OutFile $kubectlExe -UseBasicParsing
    
    # Move to Program Files
    $programFiles = $env:ProgramFiles
    $kubectlDir = "$programFiles\kubectl"
    if (!(Test-Path $kubectlDir)) {
        New-Item -ItemType Directory -Path $kubectlDir -Force | Out-Null
    }
    
    Copy-Item $kubectlExe "$kubectlDir\kubectl.exe" -Force
    
    # Add to PATH if not already there
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", "Machine")
    if ($currentPath -notlike "*$kubectlDir*") {
        [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$kubectlDir", "Machine")
        $env:PATH = "$env:PATH;$kubectlDir"
    }
    
    # Cleanup
    Remove-Item $kubectlExe -Force -ErrorAction SilentlyContinue
    
    Write-Host "✅ kubectl installed successfully" -ForegroundColor Green
}

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin) {
    Write-Host "⚠️  This script requires administrator privileges to install tools." -ForegroundColor Yellow
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    exit 1
}

# Check if doctl is installed
try {
    $null = Get-Command doctl -ErrorAction Stop
    Write-Host "✅ doctl is already installed" -ForegroundColor Green
} catch {
    Install-Doctl
}

# Check if kubectl is installed
try {
    $null = Get-Command kubectl -ErrorAction Stop
    Write-Host "✅ kubectl is already installed" -ForegroundColor Green
} catch {
    Install-Kubectl
}

# Check if DIGITALOCEAN_ACCESS_TOKEN is set
if ([string]::IsNullOrEmpty($AccessToken)) {
    Write-Host "❌ DIGITALOCEAN_ACCESS_TOKEN is not set" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 To fix this, you need to:" -ForegroundColor Yellow
    Write-Host "1. Create a DigitalOcean Personal Access Token:" -ForegroundColor White
    Write-Host "   - Go to https://cloud.digitalocean.com/account/api/tokens" -ForegroundColor Gray
    Write-Host "   - Click 'Generate New Token'" -ForegroundColor Gray
    Write-Host "   - Give it a name like 'GitOps-Runner'" -ForegroundColor Gray
    Write-Host "   - Select 'Read' and 'Write' scopes" -ForegroundColor Gray
    Write-Host "   - Copy the token" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Add it as a repository secret:" -ForegroundColor White
    Write-Host "   - Go to your GitHub repository settings" -ForegroundColor Gray
    Write-Host "   - Navigate to Secrets and variables > Actions" -ForegroundColor Gray
    Write-Host "   - Add a new repository secret named 'DIGITALOCEAN_ACCESS_TOKEN'" -ForegroundColor Gray
    Write-Host "   - Paste your DigitalOcean token as the value" -ForegroundColor Gray
    Write-Host ""
    Write-Host "3. Set the environment variable locally:" -ForegroundColor White
    Write-Host "   `$env:DIGITALOCEAN_ACCESS_TOKEN = 'your-token-here'" -ForegroundColor Gray
    Write-Host ""
    exit 1
}

Write-Host "✅ DIGITALOCEAN_ACCESS_TOKEN is set" -ForegroundColor Green

# Authenticate with DigitalOcean
Write-Host "🔐 Authenticating with DigitalOcean..." -ForegroundColor Yellow
doctl auth init --access-token $AccessToken

# Configure kubectl for DOKS
Write-Host "⚙️  Configuring kubectl for DOKS cluster..." -ForegroundColor Yellow
doctl kubernetes cluster kubeconfig save $ClusterId

# Verify cluster connectivity
Write-Host "🔍 Testing cluster connectivity..." -ForegroundColor Yellow
try {
    kubectl cluster-info | Out-Null
    Write-Host "✅ Successfully connected to DOKS cluster" -ForegroundColor Green
    kubectl cluster-info
} catch {
    Write-Host "❌ Failed to connect to DOKS cluster" -ForegroundColor Red
    exit 1
}

# Check ArgoCD namespace
Write-Host "🔍 Checking ArgoCD installation..." -ForegroundColor Yellow
try {
    kubectl get namespace argocd | Out-Null
    Write-Host "✅ ArgoCD namespace found" -ForegroundColor Green
} catch {
    Write-Host "❌ ArgoCD namespace not found" -ForegroundColor Red
    Write-Host "Please install ArgoCD on your DOKS cluster first" -ForegroundColor Yellow
    exit 1
}

# Test ArgoCD connectivity
Write-Host "🔍 Testing ArgoCD connectivity..." -ForegroundColor Yellow
try {
    kubectl get applications -n argocd | Out-Null
    Write-Host "✅ ArgoCD is accessible" -ForegroundColor Green
} catch {
    Write-Host "⚠️  ArgoCD applications not accessible (this might be normal if no apps are deployed yet)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 DOKS runner setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Configuration Summary:" -ForegroundColor Cyan
Write-Host "  • Cluster: $ClusterName" -ForegroundColor White
Write-Host "  • Cluster ID: $ClusterId" -ForegroundColor White
Write-Host "  • doctl version: $(doctl version)" -ForegroundColor White
Write-Host "  • kubectl version: $(kubectl version --client --short)" -ForegroundColor White
Write-Host "  • Current context: $(kubectl config current-context)" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Next Steps:" -ForegroundColor Cyan
Write-Host "  • Your system is now configured for DOKS" -ForegroundColor White
Write-Host "  • GitHub Actions workflows will use this DOKS cluster" -ForegroundColor White
Write-Host "  • Make sure DIGITALOCEAN_ACCESS_TOKEN is set in repository secrets" -ForegroundColor White
Write-Host "  • Test your GitOps workflows" -ForegroundColor White
