apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-spring-backend-saipriya
  namespace: ai-spring-backend-saipriya-dev
  labels:
    app: ai-spring-backend-saipriya
    component: springboot-backend
    version: v1.0.0
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-spring-backend-saipriya
      app.kubernetes.io/name: ai-spring-backend-saipriya
      app.kubernetes.io/version: "1.0.0"
      app.kubernetes.io/managed-by: argocd
  template:
    metadata:
      labels:
        app: ai-spring-backend-saipriya
        app.kubernetes.io/name: ai-spring-backend-saipriya
        app.kubernetes.io/version: "1.0.0"
        app.kubernetes.io/managed-by: argocd
        component: springboot-backend
        version: v1.0.0
        environment: dev
    spec:
      imagePullSecrets:
      - name: doks-registry
      # Backend Applications - Managed Database connectivity check
      initContainers:
      - name: wait-for-managed-postgres
        image: postgres:13-alpine
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com -p 25060 -U spring_dev_user; do
            echo "Waiting for managed PostgreSQL database to be ready..."
            sleep 2
          done
          echo "Managed PostgreSQL database is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: DB_PASSWORD
        - name: PGSSLMODE
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: DB_SSL_MODE
      containers:
      - name: ai-spring-backend-saipriya
        image: registry.digitalocean.com/doks-registry/ai-spring-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        # Environment Variables from ConfigMap
        envFrom:
        - configMapRef:
            name: ai-spring-backend-saipriya-config
        # Backend Applications - Full secret environment variables
        env:
        # Spring Boot specific database configuration
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: DB_USER
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: DB_PASSWORD
        - name: SPRING_MAIL_USERNAME
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: SMTP_USER
        - name: SPRING_MAIL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: SMTP_PASS
        - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: GOOGLE_CLIENT_ID
        - name: SPRING_SECURITY_OAUTH2_CLIENT_REGISTRATION_GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-spring-backend-saipriya-secrets
              key: GOOGLE_CLIENT_SECRET
        # Health Checks - Application Type Specific
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
