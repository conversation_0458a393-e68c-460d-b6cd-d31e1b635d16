# Cross-Repository GitOps Workflow

This document describes the new cross-repository GitOps workflow that automatically triggers ArgoCD deployments when code is merged to the main branch of your application repository.

## Overview

The cross-repository GitOps workflow enables automatic deployment of applications to Kubernetes using ArgoCD when feature branches are merged into the main branch. This workflow integrates with your existing CI/CD pipeline and maintains the same ArgoCD configuration structure as the existing issue-based deployment system.

## Architecture

```mermaid
graph LR
    A[Application Repository] --> B[CI/CD Pipeline]
    B --> C[Docker Build & Push]
    C --> D[Repository Dispatch]
    D --> E[GitOps Repository]
    E --> F[Manifest Generation]
    F --> G[ArgoCD Deployment]
    G --> H[Kubernetes Cluster]
```

### Components

1. **Application Repository**: Your application code repository with CI/CD pipeline
2. **GitOps Repository**: This repository (`gitops-argocd-apps`) containing ArgoCD configurations
3. **Repository Dispatch**: GitHub API mechanism for cross-repository communication
4. **Manifest Generation**: Dynamic creation of ArgoCD and Kubernetes manifests
5. **ArgoCD Deployment**: Automatic application of manifests to the cluster

## Workflow Files

### New Files Created

1. **`.github/workflows/deploy-from-cicd.yaml`** - Main workflow triggered by repository dispatch
2. **`scripts/generate-manifests-cicd.ps1`** - Dynamic manifest generation script
3. **`scripts/environment-config.ps1`** - Environment-specific configuration
4. **`scripts/test-cicd-integration.ps1`** - Integration testing script
5. **`docs/application-cicd-integration.md`** - Application integration guide

### Workflow Triggers

- **Repository Dispatch Event**: `deploy-to-argocd`
- **Source**: Application repository CI/CD pipeline
- **Trigger Condition**: Merge commit to main branch

## Integration Process

### 1. Application Repository Setup

Add the following step to your application's CI/CD workflow:

```yaml
- name: 🚀 Trigger GitOps Deployment
  if: github.ref == 'refs/heads/main' && github.event_name == 'push'
  uses: actions/github-script@v7
  with:
    github-token: ${{ secrets.GITOPS_DEPLOY_TOKEN }}
    script: |
      const commit = await github.rest.git.getCommit({
        owner: context.repo.owner,
        repo: context.repo.repo,
        commit_sha: context.sha
      });
      
      const isMergeCommit = commit.data.parents.length > 1;
      
      if (!isMergeCommit) {
        console.log('Not a merge commit, skipping GitOps deployment');
        return;
      }
      
      await github.rest.repos.createDispatchEvent({
        owner: 'ChidhagniConsulting',
        repo: 'gitops-argocd-apps',
        event_type: 'deploy-to-argocd',
        client_payload: {
          app_name: 'Your App Name',
          project_id: 'your-app-id',
          environment: 'dev',
          docker_image: 'your-registry/your-app',
          docker_tag: '${{ steps.docker-build.outputs.image-tag }}',
          source_repo: `${context.repo.owner}/${context.repo.repo}`,
          source_branch: 'main',
          commit_sha: context.sha
        }
      });
```

### 2. Required Secrets

Create a GitHub Personal Access Token with `repo` and `workflow` scopes and add it as `GITOPS_DEPLOY_TOKEN` secret in your application repository.

### 3. Environment Configuration

The workflow supports three environments with different configurations:

#### Development Environment
- **Trigger**: Merge to main branch
- **Replicas**: 1
- **Resources**: 256Mi memory, 100m CPU (requests)
- **Database**: localhost (for development)
- **Auto-deployment**: Enabled

#### Staging Environment
- **Trigger**: Release tag creation
- **Replicas**: 2
- **Resources**: 512Mi memory, 200m CPU (requests)
- **Database**: Dedicated PostgreSQL service
- **Features**: Ingress, monitoring, autoscaling

#### Production Environment
- **Trigger**: Manual approval or specific tag pattern
- **Replicas**: 3+
- **Resources**: 1Gi+ memory, 500m+ CPU (requests)
- **Features**: Full monitoring, autoscaling, pod anti-affinity

## Workflow Execution

### 1. Trigger Detection
- Application CI/CD completes successfully
- Merge commit to main branch is detected
- Repository dispatch event is sent to GitOps repository

### 2. Payload Validation
- Validates required fields (app_name, project_id, environment, docker_image, docker_tag)
- Checks project ID format (lowercase alphanumeric with hyphens)
- Validates environment (dev, staging, production)

### 3. Manifest Generation
- Loads environment-specific configuration
- Processes ArgoCD and Kubernetes templates
- Replaces placeholders with actual values
- Creates project directory structure

### 4. File Validation
- Validates YAML syntax
- Checks required files exist
- Verifies Docker image tag is correctly set

### 5. Git Operations
- Commits generated manifests
- Pushes changes to main branch
- Includes deployment metadata in commit message

### 6. ArgoCD Deployment
- Applies ArgoCD Project manifest
- Applies ArgoCD Application manifest
- Waits for application creation
- Triggers initial sync

## Generated Structure

The workflow generates the same directory structure as the existing issue-based system:

```
your-app-id/
├── argocd/
│   ├── application.yaml
│   └── project.yaml
└── k8s/
    ├── namespace.yaml
    ├── deployment.yaml
    ├── service.yaml
    ├── configmap.yaml
    └── secret.yaml
```

## Environment-Specific Features

### Resource Management
- **Dev**: Minimal resources for cost efficiency
- **Staging**: Moderate resources for testing
- **Production**: High resources for performance

### Security
- **Dev**: Relaxed security for development ease
- **Staging**: Enhanced security for testing
- **Production**: Maximum security with read-only filesystem, non-root user

### Monitoring & Scaling
- **Dev**: Basic health checks
- **Staging**: Monitoring and basic autoscaling
- **Production**: Full monitoring, advanced autoscaling, pod anti-affinity

## Testing

### Manual Testing
```bash
# Test manifest generation
./scripts/test-cicd-integration.ps1 -DryRun

# Test with cleanup
./scripts/test-cicd-integration.ps1 -DryRun -CleanupAfter

# Test specific environment
./scripts/test-cicd-integration.ps1 -Environment "staging" -DockerTag "v1.0.0"
```

### Automated Testing
The workflow includes comprehensive validation:
- Prerequisites checking
- Manifest generation testing
- File validation
- YAML syntax checking
- ArgoCD deployment simulation

## Comparison with Issue-Based Workflow

| Feature | Issue-Based | CI/CD-Based |
|---------|-------------|-------------|
| **Trigger** | GitHub Issue | Merge to main |
| **Input Method** | Form fields | CI/CD parameters |
| **Docker Image** | Manual entry | Automatic from CI/CD |
| **Environment** | Manual selection | Branch-based logic |
| **Validation** | Form validation | Payload validation |
| **Use Case** | Manual deployments | Automated deployments |

## Coexistence

Both workflows can coexist:
- **Issue-based**: For manual deployments, testing, hotfixes
- **CI/CD-based**: For automated deployments from main branch
- **Same structure**: Both generate identical ArgoCD configurations
- **Same templates**: Both use the same manifest templates

## Troubleshooting

### Common Issues

1. **Repository Dispatch Not Triggered**
   - Check merge commit detection logic
   - Verify token permissions
   - Ensure correct repository names

2. **Manifest Generation Fails**
   - Validate payload format
   - Check template file existence
   - Verify environment configuration

3. **ArgoCD Deployment Fails**
   - Check cluster connectivity
   - Verify ENABLE_AUTO_DEPLOY variable
   - Validate YAML syntax

### Debug Commands

```bash
# Check workflow logs
gh run list --repo ChidhagniConsulting/gitops-argocd-apps

# Test manifest generation locally
./scripts/generate-manifests-cicd.ps1 -AppName "Test App" -ProjectId "test-app" -Environment "dev" -DockerImage "test/app" -DockerTag "latest"

# Validate environment config
./scripts/environment-config.ps1 -Environment "dev" -ProjectId "test-app"
```

## Security Considerations

- Use repository secrets for sensitive tokens
- Limit token permissions to minimum required scopes
- Regularly rotate access tokens
- Monitor dispatch events for unauthorized usage
- Validate all input parameters

## Future Enhancements

- Support for multiple environments per dispatch
- Integration with GitHub Environments for approval workflows
- Automatic rollback on deployment failures
- Integration with monitoring systems for deployment validation
- Support for blue-green and canary deployments
