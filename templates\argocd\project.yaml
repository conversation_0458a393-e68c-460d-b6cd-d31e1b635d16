apiVersion: argoproj.io/v1alpha1
kind: AppProject
metadata:
  name: {{PROJECT_ID}}-project
  namespace: argocd
  labels:
    app.kubernetes.io/name: {{PROJECT_ID}}-project
    environment: {{ENVIRONMENT}}
spec:
  description: "{{APP_NAME}} Project for GitOps deployment"
  sourceRepos:
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps.git'
  - 'https://github.com/ChidhagniConsulting/gitops-argocd-apps'
  destinations:
  - namespace: {{NAMESPACE}}
    server: {{#if CLUSTER_SERVER}}{{CLUSTER_SERVER}}{{else}}https://kubernetes.default.svc{{/if}}
  - namespace: argocd
    server: https://kubernetes.default.svc
  clusterResourceWhitelist:
  - group: ''
    kind: Namespace
  - group: ''
    kind: PersistentVolume
  - group: storage.k8s.io
    kind: StorageClass
  namespaceResourceWhitelist:
  - group: ''
    kind: ConfigMap
  - group: ''
    kind: Secret
  - group: ''
    kind: Service
  - group: ''
    kind: PersistentVolumeClaim
  - group: apps
    kind: Deployment
  - group: batch
    kind: Job
  - group: networking.k8s.io
    kind: Ingress
  roles:
  - name: admin
    description: "Admin access to {{PROJECT_ID}} project"
    policies:
    - p, proj:{{PROJECT_ID}}-project:admin, applications, *, {{PROJECT_ID}}-project/*, allow
    - p, proj:{{PROJECT_ID}}-project:admin, repositories, *, *, allow
    groups:
    - argocd:admin
