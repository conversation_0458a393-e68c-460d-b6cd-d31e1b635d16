apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-django-backend
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-django-backend
    app.kubernetes.io/part-of: ai-django-backend
    environment: dev
    app-type: django-backend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-django-backend-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: ai-django-backend/k8s
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-django-backend-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "AI Django Backend - Django Backend API"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "django-backend"
  - name: Configuration
    value: "Django ORM, REST framework, admin interface"
