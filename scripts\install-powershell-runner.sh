#!/bin/bash
#
# Quick PowerShell installation script for GitHub Actions self-hosted runners
# This script installs PowerShell on Linux systems to fix the GitOps workflow error
#

set -e

echo "🔧 Installing PowerShell for GitHub Actions Runner"
echo "================================================="

# Function to detect OS
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo "$ID"
    else
        echo "unknown"
    fi
}

# Function to install PowerShell on Ubuntu/Debian
install_powershell_debian() {
    echo "📦 Installing PowerShell on Ubuntu/Debian..."
    
    # Download and install Microsoft package repository
    wget -q https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O /tmp/packages-microsoft-prod.deb
    sudo dpkg -i /tmp/packages-microsoft-prod.deb
    
    # Update package list and install PowerShell
    sudo apt-get update
    sudo apt-get install -y powershell
    
    # Clean up
    rm -f /tmp/packages-microsoft-prod.deb
}

# Function to install PowerShell on CentOS/RHEL/Fedora
install_powershell_rhel() {
    echo "📦 Installing PowerShell on CentOS/RHEL/Fedora..."
    
    # Add Microsoft repository
    sudo rpm --import https://packages.microsoft.com/keys/microsoft.asc
    sudo curl -o /etc/yum.repos.d/microsoft.repo https://packages.microsoft.com/config/rhel/8/prod.repo
    
    # Install PowerShell
    sudo yum install -y powershell
}

# Function to verify PowerShell installation
verify_powershell() {
    echo "🔍 Verifying PowerShell installation..."
    
    if command -v pwsh >/dev/null 2>&1; then
        echo "✅ PowerShell is available"
        pwsh --version
        return 0
    else
        echo "❌ PowerShell installation failed"
        return 1
    fi
}

# Main execution
echo "🔍 Detecting operating system..."
OS=$(detect_os)
echo "Detected OS: $OS"

# Check if PowerShell is already installed
if command -v pwsh >/dev/null 2>&1; then
    echo "✅ PowerShell is already installed"
    pwsh --version
    exit 0
fi

# Install PowerShell based on OS
case $OS in
    ubuntu|debian)
        install_powershell_debian
        ;;
    centos|rhel|fedora)
        install_powershell_rhel
        ;;
    *)
        echo "❌ Unsupported operating system: $OS"
        echo "Please install PowerShell manually:"
        echo "https://docs.microsoft.com/en-us/powershell/scripting/install/installing-powershell-core-on-linux"
        exit 1
        ;;
esac

# Verify installation
if verify_powershell; then
    echo ""
    echo "🎉 PowerShell installation completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Restart your GitHub Actions runner service"
    echo "2. Test the GitOps workflow with a new GitHub issue"
    echo "3. Monitor workflow logs to ensure PowerShell is working"
    echo ""
    echo "Runner service restart commands:"
    echo "  sudo systemctl restart actions.runner.*"
    echo "  # or"
    echo "  sudo systemctl restart arc-runner-set-*"
else
    echo "❌ PowerShell installation verification failed"
    exit 1
fi
