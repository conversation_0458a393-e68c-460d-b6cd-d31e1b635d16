#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Environment-specific configuration for GitOps deployments
    
.DESCRIPTION
    This script provides environment-specific configuration logic for GitOps deployments,
    including resource allocation, replica counts, and deployment strategies based on
    the target environment (dev, staging, production).
    
.PARAMETER Environment
    Target environment (dev, staging, production)
    
.PARAMETER ProjectId
    Project identifier
    
.PARAMETER GetConfig
    Return configuration as JSON object
    
.EXAMPLE
    ./environment-config.ps1 -Environment "dev" -ProjectId "auth-app" -GetConfig
    
.EXAMPLE
    $config = ./environment-config.ps1 -Environment "production" -ProjectId "auth-app" -GetConfig | ConvertFrom-Json
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "production", "feature")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$ProjectId,
    
    [Parameter(Mandatory=$false)]
    [switch]$GetConfig
)

# Environment-specific configurations
$environmentConfigs = @{
    dev = @{
        replicas = 1
        resources = @{
            requests = @{
                memory = "256Mi"
                cpu = "100m"
            }
            limits = @{
                memory = "512Mi"
                cpu = "500m"
            }
        }
        database = @{
            enabled = $true
            host = "private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com"  # Managed DigitalOcean database
            storage = "1Gi"
            resources = @{
                requests = @{
                    memory = "128Mi"
                    cpu = "50m"
                }
                limits = @{
                    memory = "256Mi"
                    cpu = "200m"
                }
            }
        }
        ingress = @{
            enabled = $false
            host = "$ProjectId-dev.local"
            tls = $false
        }
        monitoring = @{
            enabled = $false
        }
        autoscaling = @{
            enabled = $false
        }
        nodeSelector = @{}
        tolerations = @()
        affinity = @{}
    }
    
    staging = @{
        replicas = 2
        resources = @{
            requests = @{
                memory = "512Mi"
                cpu = "200m"
            }
            limits = @{
                memory = "1Gi"
                cpu = "1000m"
            }
        }
        database = @{
            enabled = $true
            host = "$ProjectId-postgres.staging"  # Dedicated postgres service
            storage = "5Gi"
            resources = @{
                requests = @{
                    memory = "256Mi"
                    cpu = "100m"
                }
                limits = @{
                    memory = "512Mi"
                    cpu = "500m"
                }
            }
        }
        ingress = @{
            enabled = $true
            host = "$ProjectId-staging.yourdomain.com"
            tls = $true
        }
        monitoring = @{
            enabled = $true
        }
        autoscaling = @{
            enabled = $true
            minReplicas = 2
            maxReplicas = 5
            targetCPUUtilization = 70
        }
        nodeSelector = @{
            "node-type" = "staging"
        }
        tolerations = @()
        affinity = @{}
    }
    
    production = @{
        replicas = 3
        resources = @{
            requests = @{
                memory = "1Gi"
                cpu = "500m"
            }
            limits = @{
                memory = "2Gi"
                cpu = "2000m"
            }
        }
        database = @{
            enabled = $true
            host = "$ProjectId-postgres.production"  # Dedicated postgres service
            storage = "20Gi"
            resources = @{
                requests = @{
                    memory = "512Mi"
                    cpu = "200m"
                }
                limits = @{
                    memory = "1Gi"
                    cpu = "1000m"
                }
            }
        }
        ingress = @{
            enabled = $true
            host = "$ProjectId.yourdomain.com"
            tls = $true
        }
        monitoring = @{
            enabled = $true
        }
        autoscaling = @{
            enabled = $true
            minReplicas = 3
            maxReplicas = 10
            targetCPUUtilization = 60
        }
        nodeSelector = @{
            "node-type" = "production"
        }
        tolerations = @(
            @{
                key = "production"
                operator = "Equal"
                value = "true"
                effect = "NoSchedule"
            }
        )
        affinity = @{
            podAntiAffinity = @{
                preferredDuringSchedulingIgnoredDuringExecution = @(
                    @{
                        weight = 100
                        podAffinityTerm = @{
                            labelSelector = @{
                                matchExpressions = @(
                                    @{
                                        key = "app"
                                        operator = "In"
                                        values = @($ProjectId)
                                    }
                                )
                            }
                            topologyKey = "kubernetes.io/hostname"
                        }
                    }
                )
            }
        }
    }
}

# Get configuration for the specified environment
$config = $environmentConfigs[$Environment]

if (-not $config) {
    Write-Error "Invalid environment: $Environment"
    exit 1
}

# Replace ProjectId placeholders in configuration
$configJson = $config | ConvertTo-Json -Depth 10
$configJson = $configJson -replace '\$ProjectId', $ProjectId
$config = $configJson | ConvertFrom-Json

# Add computed values
$config | Add-Member -NotePropertyName "namespace" -NotePropertyValue "$ProjectId-$Environment"
$config | Add-Member -NotePropertyName "environment" -NotePropertyValue $Environment
$config | Add-Member -NotePropertyName "projectId" -NotePropertyValue $ProjectId

# Environment-specific health check configurations
switch ($Environment) {
    "dev" {
        $config | Add-Member -NotePropertyName "healthCheck" -NotePropertyValue @{
            initialDelaySeconds = 30
            periodSeconds = 30
            timeoutSeconds = 10
            failureThreshold = 5
            path = "/actuator/health"
        }
    }
    "staging" {
        $config | Add-Member -NotePropertyName "healthCheck" -NotePropertyValue @{
            initialDelaySeconds = 45
            periodSeconds = 20
            timeoutSeconds = 5
            failureThreshold = 3
            path = "/actuator/health"
        }
    }
    "production" {
        $config | Add-Member -NotePropertyName "healthCheck" -NotePropertyValue @{
            initialDelaySeconds = 60
            periodSeconds = 10
            timeoutSeconds = 5
            failureThreshold = 3
            path = "/actuator/health"
        }
    }
}

# Environment-specific security configurations
switch ($Environment) {
    "dev" {
        $config | Add-Member -NotePropertyName "security" -NotePropertyValue @{
            runAsNonRoot = $false
            readOnlyRootFilesystem = $false
            allowPrivilegeEscalation = $true
        }
    }
    "staging" {
        $config | Add-Member -NotePropertyName "security" -NotePropertyValue @{
            runAsNonRoot = $true
            runAsUser = 1000
            readOnlyRootFilesystem = $true
            allowPrivilegeEscalation = $false
        }
    }
    "production" {
        $config | Add-Member -NotePropertyName "security" -NotePropertyValue @{
            runAsNonRoot = $true
            runAsUser = 1000
            readOnlyRootFilesystem = $true
            allowPrivilegeEscalation = $false
            capabilities = @{
                drop = @("ALL")
            }
        }
    }
}

if ($GetConfig) {
    # Return configuration as JSON
    $config | ConvertTo-Json -Depth 10
} else {
    # Display configuration summary
    Write-Host "Environment Configuration for $Environment" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "Project ID: $ProjectId" -ForegroundColor Cyan
    Write-Host "Namespace: $($config.namespace)" -ForegroundColor Cyan
    Write-Host "Replicas: $($config.replicas)" -ForegroundColor Cyan
    Write-Host "Memory Request: $($config.resources.requests.memory)" -ForegroundColor Cyan
    Write-Host "Memory Limit: $($config.resources.limits.memory)" -ForegroundColor Cyan
    Write-Host "CPU Request: $($config.resources.requests.cpu)" -ForegroundColor Cyan
    Write-Host "CPU Limit: $($config.resources.limits.cpu)" -ForegroundColor Cyan
    Write-Host "Database Enabled: $($config.database.enabled)" -ForegroundColor Cyan
    Write-Host "Database Host: $($config.database.host)" -ForegroundColor Cyan
    Write-Host "Ingress Enabled: $($config.ingress.enabled)" -ForegroundColor Cyan
    if ($config.ingress.enabled) {
        Write-Host "Ingress Host: $($config.ingress.host)" -ForegroundColor Cyan
    }
    Write-Host "Monitoring Enabled: $($config.monitoring.enabled)" -ForegroundColor Cyan
    Write-Host "Autoscaling Enabled: $($config.autoscaling.enabled)" -ForegroundColor Cyan
    if ($config.autoscaling.enabled) {
        Write-Host "Min Replicas: $($config.autoscaling.minReplicas)" -ForegroundColor Cyan
        Write-Host "Max Replicas: $($config.autoscaling.maxReplicas)" -ForegroundColor Cyan
    }
}
