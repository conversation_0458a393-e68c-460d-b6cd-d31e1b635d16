apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: ai-nest-backend-shashank
  namespace: argocd
  labels:
    app.kubernetes.io/name: ai-nest-backend-shashank
    app.kubernetes.io/part-of: ai-nest-backend-shashank
    environment: dev
    app-type: nest-backend
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: ai-nest-backend-shashank-project
  source:
    repoURL: https://github.com/ChidhagniConsulting/gitops-argocd-apps
    targetRevision: main
    path: ai-nest-backend-shashank/k8s
  destination:
    server: https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com
    namespace: ai-nest-backend-shashank-dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  info:
  - name: Description
    value: "ai-nest-backend-shashank - NestJS Backend API"
  - name: Repository
    value: "https://github.com/ChidhagniConsulting/gitops-argocd-apps"
  - name: Environment
    value: "dev"
  - name: Application Type
    value: "nest-backend"
  - name: Configuration
    value: "TypeScript, decorators, dependency injection"
