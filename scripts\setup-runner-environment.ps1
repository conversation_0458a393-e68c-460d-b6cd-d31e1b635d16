#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Setup self-hosted GitHub Actions runner environment for GitOps automation

.DESCRIPTION
    This script sets up the required tools and configuration for self-hosted GitHub Actions runners
    to work with the GitOps automation workflow. It installs kubectl, configures DOKS access,
    and verifies ArgoCD connectivity.

.PARAMETER InstallKubectl
    Install kubectl if not present

.PARAMETER ConfigureDOKS
    Configure kubectl to use DOKS context

.PARAMETER VerifySetup
    Verify the complete setup

.PARAMETER DryRun
    Show what would be done without making changes

.EXAMPLE
    ./setup-runner-environment.ps1 -InstallKubectl -ConfigureDOKS -VerifySetup
    
.EXAMPLE
    ./setup-runner-environment.ps1 -VerifySetup -DryRun
#>

param(
    [switch]$InstallKubectl,
    [switch]$InstallPowerShell,
    [switch]$ConfigureDOKS,
    [switch]$VerifySetup,
    [switch]$DryRun
)

Write-Host "🔧 GitHub Actions Runner Environment Setup" -ForegroundColor Green
Write-Host "==========================================" -ForegroundColor Green

# Function to check if command exists
function Test-CommandExists {
    param([string]$Command)
    try {
        $null = Get-Command $Command -ErrorAction Stop
        return $true
    } catch {
        return $false
    }
}

# Function to install kubectl
function Install-Kubectl {
    Write-Host "`n📦 Installing kubectl..." -ForegroundColor Yellow
    
    if ($DryRun) {
        Write-Host "🔍 DRY RUN - Would install kubectl" -ForegroundColor Cyan
        return $true
    }
    
    try {
        # Detect OS
        $os = $env:OS
        if ($IsLinux) {
            Write-Host "🐧 Detected Linux - installing kubectl for Linux" -ForegroundColor Cyan
            
            # Download kubectl for Linux
            $kubectlUrl = "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            Invoke-WebRequest -Uri $kubectlUrl -OutFile "/tmp/kubectl"
            chmod +x /tmp/kubectl
            sudo mv /tmp/kubectl /usr/local/bin/kubectl
            
        } elseif ($IsWindows) {
            Write-Host "🪟 Detected Windows - installing kubectl for Windows" -ForegroundColor Cyan
            
            # Download kubectl for Windows
            $kubectlUrl = "https://dl.k8s.io/release/v1.28.0/bin/windows/amd64/kubectl.exe"
            $kubectlPath = "$env:USERPROFILE\kubectl.exe"
            Invoke-WebRequest -Uri $kubectlUrl -OutFile $kubectlPath
            
            # Add to PATH if not already there
            $currentPath = [Environment]::GetEnvironmentVariable("PATH", "User")
            if ($currentPath -notlike "*$env:USERPROFILE*") {
                [Environment]::SetEnvironmentVariable("PATH", "$currentPath;$env:USERPROFILE", "User")
                $env:PATH = "$env:PATH;$env:USERPROFILE"
            }
        } else {
            Write-Host "❌ Unsupported operating system" -ForegroundColor Red
            return $false
        }
        
        # Verify installation
        Start-Sleep 2
        if (Test-CommandExists "kubectl") {
            $version = kubectl version --client --short 2>$null
            Write-Host "✅ kubectl installed successfully: $version" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ kubectl installation failed" -ForegroundColor Red
            return $false
        }
        
    } catch {
        Write-Host "❌ Error installing kubectl: $_" -ForegroundColor Red
        return $false
    }
}

# Function to install PowerShell
function Install-PowerShell {
    Write-Host "`n📦 Installing PowerShell..." -ForegroundColor Yellow

    if ($DryRun) {
        Write-Host "🔍 DRY RUN - Would install PowerShell" -ForegroundColor Cyan
        return $true
    }

    try {
        if ($IsLinux) {
            Write-Host "🐧 Detected Linux - installing PowerShell for Linux" -ForegroundColor Cyan

            # Install PowerShell on Linux
            if (Test-CommandExists "apt-get") {
                # Ubuntu/Debian
                wget -q https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O /tmp/packages-microsoft-prod.deb
                sudo dpkg -i /tmp/packages-microsoft-prod.deb
                sudo apt-get update
                sudo apt-get install -y powershell
            } elseif (Test-CommandExists "yum") {
                # CentOS/RHEL/Fedora
                sudo yum install -y https://packages.microsoft.com/config/rhel/8/packages-microsoft-prod.rpm
                sudo yum install -y powershell
            } else {
                Write-Host "❌ Unsupported package manager" -ForegroundColor Red
                return $false
            }

        } elseif ($IsWindows) {
            Write-Host "🪟 PowerShell should already be available on Windows" -ForegroundColor Cyan
            return $true
        } else {
            Write-Host "❌ Unsupported operating system" -ForegroundColor Red
            return $false
        }

        # Verify installation
        Start-Sleep 2
        if (Test-CommandExists "pwsh") {
            $version = pwsh --version 2>$null
            Write-Host "✅ PowerShell installed successfully: $version" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ PowerShell installation failed" -ForegroundColor Red
            return $false
        }

    } catch {
        Write-Host "❌ Error installing PowerShell: $_" -ForegroundColor Red
        return $false
    }
}

# Function to configure DOKS context
function Set-DOKSContext {
    Write-Host "`n🔧 Configuring kubectl for DOKS..." -ForegroundColor Yellow

    if ($DryRun) {
        Write-Host "🔍 DRY RUN - Would configure DOKS context" -ForegroundColor Cyan
        return $true
    }

    try {
        # Check available contexts
        $contexts = kubectl config get-contexts -o name 2>$null
        Write-Host "Available contexts: $($contexts -join ', ')" -ForegroundColor Cyan

        # Try to find a DOKS context (usually contains 'do-' or 'doks')
        $doksContext = $contexts | Where-Object { $_ -match 'do-|doks|digitalocean' } | Select-Object -First 1

        if ($doksContext) {
            Write-Host "🎯 Using DOKS context: $doksContext" -ForegroundColor Yellow
            kubectl config use-context $doksContext

            # Verify context
            $currentContext = kubectl config current-context 2>$null
            if ($currentContext -eq $doksContext) {
                Write-Host "✅ kubectl configured to use DOKS context: $doksContext" -ForegroundColor Green
                return $true
            } else {
                Write-Host "❌ Failed to set DOKS context" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "⚠️ No DOKS context found. Please configure kubectl manually." -ForegroundColor Yellow
            Write-Host "Current context: $(kubectl config current-context 2>$null)" -ForegroundColor Cyan
            return $true
        }

    } catch {
        Write-Host "❌ Error configuring DOKS context: $_" -ForegroundColor Red
        return $false
    }
}

# Function to verify complete setup
function Test-RunnerSetup {
    Write-Host "`n🔍 Verifying runner environment setup..." -ForegroundColor Yellow
    
    $issues = @()
    $warnings = @()
    
    # Check kubectl
    if (Test-CommandExists "kubectl") {
        try {
            $version = kubectl version --client --short 2>$null
            Write-Host "✅ kubectl is available: $version" -ForegroundColor Green
        } catch {
            $issues += "kubectl version check failed"
        }
    } else {
        $issues += "kubectl is not installed"
    }
    
    # Check cluster connectivity
    try {
        $clusterInfo = kubectl cluster-info 2>$null
        if ($clusterInfo) {
            Write-Host "✅ Kubernetes cluster is accessible" -ForegroundColor Green
        } else {
            $warnings += "Kubernetes cluster is not accessible"
        }
    } catch {
        $warnings += "Kubernetes cluster connectivity check failed"
    }
    
    # Check cluster connectivity
    if (Test-CommandExists "kubectl") {
        try {
            $null = kubectl cluster-info --request-timeout=10s 2>$null
            Write-Host "✅ Kubernetes cluster is accessible" -ForegroundColor Green
            
            # Check ArgoCD
            try {
                $null = kubectl get crd applications.argoproj.io 2>$null
                Write-Host "✅ ArgoCD CRDs are available" -ForegroundColor Green
                
                # Check ArgoCD namespace
                try {
                    $null = kubectl get namespace argocd 2>$null
                    Write-Host "✅ ArgoCD namespace exists" -ForegroundColor Green
                } catch {
                    $warnings += "ArgoCD namespace not found"
                }
            } catch {
                $warnings += "ArgoCD CRDs not found"
            }
        } catch {
            $issues += "Cannot connect to Kubernetes cluster"
        }
    }
    
    # Check PowerShell
    if (Test-CommandExists "pwsh") {
        $version = pwsh --version 2>$null
        Write-Host "✅ PowerShell is available: $version" -ForegroundColor Green
    } else {
        $issues += "PowerShell (pwsh) not found - required for GitOps automation"
    }
    
    # Report results
    Write-Host "`n📊 Setup Verification Results:" -ForegroundColor Cyan
    
    if ($issues.Count -eq 0) {
        Write-Host "🎉 All critical components are working!" -ForegroundColor Green
    } else {
        Write-Host "❌ Critical Issues Found:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "   • $issue" -ForegroundColor Red
        }
    }
    
    if ($warnings.Count -gt 0) {
        Write-Host "⚠️ Warnings:" -ForegroundColor Yellow
        foreach ($warning in $warnings) {
            Write-Host "   • $warning" -ForegroundColor Yellow
        }
    }
    
    return ($issues.Count -eq 0)
}

# Main execution
Write-Host "`n🔍 Current Environment:" -ForegroundColor Cyan
Write-Host "OS: $($PSVersionTable.OS)" -ForegroundColor Gray
Write-Host "PowerShell: $($PSVersionTable.PSVersion)" -ForegroundColor Gray
Write-Host "User: $env:USER$env:USERNAME" -ForegroundColor Gray

# Install kubectl if requested
if ($InstallKubectl) {
    if (-not (Install-Kubectl)) {
        Write-Host "❌ kubectl installation failed. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Install PowerShell if requested
if ($InstallPowerShell) {
    if (-not (Install-PowerShell)) {
        Write-Host "❌ PowerShell installation failed. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Configure DOKS if requested
if ($ConfigureDOKS) {
    if (-not (Set-DOKSContext)) {
        Write-Host "❌ DOKS configuration failed. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Verify setup if requested
if ($VerifySetup) {
    $setupValid = Test-RunnerSetup
    
    if ($setupValid) {
        Write-Host "`n✅ Runner environment is ready for GitOps automation!" -ForegroundColor Green
    } else {
        Write-Host "`n❌ Runner environment has issues that need to be resolved." -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Ensure ENABLE_AUTO_DEPLOY=true in repository variables" -ForegroundColor White
Write-Host "2. Test the workflow with a new GitHub issue" -ForegroundColor White
Write-Host "3. Monitor workflow logs for any remaining issues" -ForegroundColor White
