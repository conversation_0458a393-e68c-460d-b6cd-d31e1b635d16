# Backend Deployment Payloads with Managed Database

This document provides the confirmed payload structure for deploying Spring Boot, Django, and NestJS backend applications through the GitOps automation system using the managed DigitalOcean PostgreSQL database.

## Managed Database Configuration

All backend applications now connect to a managed DigitalOcean PostgreSQL database with the following default configuration:

- **Host**: `private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com`
- **Port**: `25060`
- **Database**: `spring_dev_db`
- **Username**: `spring_dev_user`
- **Password**: `AVNS_0bYzt0GZdky7rnP8Kl7`
- **SSL Mode**: `require`

## Payload Structure for All Backend Types

### Required Fields

All backend applications require these fields in the dispatch payload:

```json
{
  "app_name": "Application Display Name",
  "project_id": "lowercase-app-id",
  "application_type": "springboot-backend|django-backend|nest-backend",
  "environment": "dev|staging|production",
  "docker_image": "registry/image-name",
  "docker_tag": "tag-version",
  "source_repo": "org/repo-name",
  "source_branch": "branch-name",
  "commit_sha": "commit-hash"
}
```

### Optional Database Override Fields

To override the default managed database configuration, include these in the `secrets_encoded` field:

```json
{
  "DB_HOST": "custom-database-host",
  "DB_PORT": "custom-port",
  "DB_NAME": "custom-database-name",
  "DB_USER": "custom-username",
  "DB_PASSWORD": "custom-password",
  "DB_SSL_MODE": "require|prefer|disable"
}
```

## Spring Boot Backend Payload

### Basic Payload

```json
{
  "app_name": "Spring Boot User API",
  "project_id": "spring-user-api",
  "application_type": "springboot-backend",
  "environment": "dev",
  "docker_image": "myorg/spring-user-api",
  "docker_tag": "v1.0.0",
  "source_repo": "myorg/spring-user-api",
  "source_branch": "main",
  "commit_sha": "abc123def456"
}
```

### With Custom Database Configuration

```json
{
  "app_name": "Spring Boot User API",
  "project_id": "spring-user-api",
  "application_type": "springboot-backend",
  "environment": "production",
  "docker_image": "myorg/spring-user-api",
  "docker_tag": "v2.1.0",
  "source_repo": "myorg/spring-user-api",
  "source_branch": "main",
  "commit_sha": "def456ghi789",
  "secrets_encoded": "base64-encoded-secrets-json"
}
```

## Django Backend Payload

### Basic Payload

```json
{
  "app_name": "Django User API",
  "project_id": "django-user-api",
  "application_type": "django-backend",
  "environment": "dev",
  "docker_image": "myorg/django-user-api",
  "docker_tag": "v1.0.0",
  "source_repo": "myorg/django-user-api",
  "source_branch": "main",
  "commit_sha": "abc123def456"
}
```

### With Custom Database Configuration

```json
{
  "app_name": "Django User API",
  "project_id": "django-user-api",
  "application_type": "django-backend",
  "environment": "production",
  "docker_image": "myorg/django-user-api",
  "docker_tag": "v2.1.0",
  "source_repo": "myorg/django-user-api",
  "source_branch": "main",
  "commit_sha": "def456ghi789",
  "secrets_encoded": "base64-encoded-secrets-json"
}
```

## NestJS Backend Payload

### Basic Payload

```json
{
  "app_name": "NestJS Auth Service",
  "project_id": "nest-auth-service",
  "application_type": "nest-backend",
  "environment": "dev",
  "docker_image": "myorg/nest-auth-service",
  "docker_tag": "v2.1.0",
  "source_repo": "myorg/nest-auth-service",
  "source_branch": "main",
  "commit_sha": "def456ghi789"
}
```

### With Custom Database Configuration

```json
{
  "app_name": "NestJS Auth Service",
  "project_id": "nest-auth-service",
  "application_type": "nest-backend",
  "environment": "production",
  "docker_image": "myorg/nest-auth-service",
  "docker_tag": "v2.1.0",
  "source_repo": "myorg/nest-auth-service",
  "source_branch": "main",
  "commit_sha": "def456ghi789",
  "secrets_encoded": "base64-encoded-secrets-json"
}
```

## Creating the secrets_encoded Field

To create the `secrets_encoded` field with managed database configuration:

### Method 1: GitHub Actions Workflow (Recommended)

Use this shell command structure in your GitHub Actions workflow:

```yaml
- name: 🔐 Create Managed Database Secrets Payload
  id: create-secrets
  run: |
    SECRETS_JSON=$(cat << EOF | base64 -w 0
    {
      "JWT_SECRET": "${{ secrets.JWT_SECRET || 'supersecretkey' }}",
      "DB_USER": "${{ secrets.DB_USER || 'spring_dev_user' }}",
      "DB_PASSWORD": "${{ secrets.DB_PASSWORD || 'AVNS_0bYzt0GZdky7rnP8Kl7' }}",
      "DB_HOST": "${{ secrets.DB_HOST || 'private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com' }}",
      "DB_PORT": "${{ secrets.DB_PORT || '25060' }}",
      "DB_NAME": "${{ secrets.DB_NAME || 'spring_dev_db' }}",
      "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE || 'require' }}",
      "SMTP_USER": "${{ secrets.SMTP_USER || '<EMAIL>' }}",
      "SMTP_PASS": "${{ secrets.SMTP_PASS || 'fqactehafmzlltzz' }}",
      "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID || '1073981864538-********************************.apps.googleusercontent.com' }}",
      "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET || '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' }}"
    }
    EOF
    )

    echo "secrets-encoded=${SECRETS_JSON}" >> $GITHUB_OUTPUT
```

### Method 2: Manual Base64 Encoding

For testing or manual deployment:

```bash
echo -n '{"DB_HOST":"private-dbaas-db-10329328-do-user-23815742-0.m.db.ondigitalocean.com","DB_PORT":"25060","DB_NAME":"spring_dev_db","DB_USER":"spring_dev_user","DB_PASSWORD":"AVNS_0bYzt0GZdky7rnP8Kl7","DB_SSL_MODE":"require","JWT_SECRET":"supersecretkey","SMTP_USER":"<EMAIL>","SMTP_PASS":"fqactehafmzlltzz","GOOGLE_CLIENT_ID":"1073981864538-********************************.apps.googleusercontent.com","GOOGLE_CLIENT_SECRET":"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"}' | base64 -w 0
```

### Step 3: Use in GitHub Actions Workflow

```yaml
- name: 🚀 Trigger GitOps Deployment
  uses: actions/github-script@v7
  with:
    github-token: ${{ secrets.GITOPS_DEPLOY_TOKEN }}
    script: |
      const payload = {
        app_name: 'ai-spring-backend-saipriya',
        project_id: 'ai-spring-backend-saipriya',
        application_type: 'springboot-backend',
        environment: 'dev',
        docker_image: 'registry.digitalocean.com/doks-registry/ai-spring-backend-saipriya',
        docker_tag: '${{ needs.build-and-push-docker.outputs.image-tag }}',
        source_repo: `${context.repo.owner}/${context.repo.repo}`,
        source_branch: '${{ github.ref_name }}',
        commit_sha: context.sha,
        secrets_encoded: '${{ steps.create-secrets.outputs.secrets-encoded }}'
      };

      await github.rest.repos.createDispatchEvent({
        owner: 'ChidhagniConsulting',
        repo: 'gitops-argocd-apps',
        event_type: 'deploy-to-argocd',
        client_payload: payload
      });
```

## Key Changes from Self-Hosted PostgreSQL

1. **No PostgreSQL manifests**: PostgreSQL StatefulSet, Service, and PVC are no longer generated
2. **Managed database connection**: Applications connect to the managed DigitalOcean database
3. **SSL required**: All connections use SSL mode 'require'
4. **Centralized database**: All applications share the same managed database instance
5. **Updated init containers**: Database readiness checks now target the managed database host and port
6. **Environment-agnostic**: Same database configuration across dev, staging, and production environments

## Testing the Configuration

Use the test script to verify the managed database configuration:

```bash
./test-dispatch-payloads.sh
```

This will test all three backend types with the managed database configuration.
