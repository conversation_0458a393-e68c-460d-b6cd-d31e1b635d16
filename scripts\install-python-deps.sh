#!/bin/bash

# Install Python dependencies for GitOps automation scripts
# This script ensures PyYAML is available for YAML validation

set -e

echo "🐍 Installing Python dependencies for GitOps automation..."

# Check if pip is available
if ! command -v pip3 >/dev/null 2>&1; then
    echo "❌ pip3 not found. Please install Python3 and pip3 first."
    exit 1
fi

# Install dependencies from requirements.txt
echo "📦 Installing Python dependencies..."
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt --user --quiet
else
    # Fallback to direct installation
    pip3 install PyYAML --user --quiet
fi

# Verify installation
echo "🔍 Verifying PyYAML installation..."
if python3 -c "import yaml; print('✅ PyYAML installed successfully')" 2>/dev/null; then
    echo "✅ All Python dependencies installed successfully"
else
    echo "❌ Failed to verify PyYAML installation"
    exit 1
fi

echo "🎉 Python dependencies setup complete!"
