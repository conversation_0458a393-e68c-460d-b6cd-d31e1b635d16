{{#if ENABLE_INGRESS}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{PROJECT_ID}}-ingress
  namespace: {{NAMESPACE}}
  labels:
    app: {{PROJECT_ID}}
    component: ingress
    environment: {{ENVIRONMENT}}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET,POST,PUT,DELETE,PATCH,OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  ingressClassName: nginx
  rules:
  - host: {{#if INGRESS_HOST}}{{INGRESS_HOST}}{{else}}{{PROJECT_ID}}.local{{/if}}
    http:
      paths:
      - path: {{#if INGRESS_PATH}}{{INGRESS_PATH}}{{else}}/{{/if}}
        pathType: Prefix
        backend:
          service:
            name: {{PROJECT_ID}}-service
            port:
              number: {{CONTAINER_PORT}}
{{/if}}
