#!/usr/bin/env python3
import sys
import os
import glob
import json
import re

def validate_yaml_file(file_path):
    """
    Validate YAML file using the best available method with enhanced error reporting.
    First tries to use PyYAML if available, falls back to basic syntax checking.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for common template processing issues first
        template_issues = check_template_processing_issues(content, file_path)
        if template_issues:
            for issue in template_issues:
                print(f'[WARN] Template issue in {file_path}: {issue}')

        # Try to use PyYAML if available
        try:
            import yaml
            parsed_yaml = yaml.safe_load(content)

            # Additional validation for Kubernetes manifests
            k8s_issues = validate_kubernetes_manifest(parsed_yaml, file_path)
            if k8s_issues:
                for issue in k8s_issues:
                    print(f'[WARN] Kubernetes issue in {file_path}: {issue}')

            print(f'[PASS] Valid YAML (PyYAML): {file_path}')
            return True
        except ImportError:
            # PyYAML not available, use basic validation
            if not validate_yaml_syntax(content, file_path):
                return False
            print(f'[PASS] Valid YAML (basic check): {file_path}')
            return True
        except Exception as e:
            print(f'[FAIL] Invalid YAML: {file_path} - {e}')
            # Try to provide more specific error information
            provide_detailed_yaml_error(content, file_path, str(e))
            return False

    except Exception as e:
        print(f'[ERROR] Error reading {file_path}: {e}')
        return False

def validate_yaml_syntax(content, file_path):
    """
    Basic YAML syntax validation without external dependencies.
    Checks for common syntax issues that would cause parsing failures.
    """
    lines = content.split('\n')

    for line_num, line in enumerate(lines, 1):
        # Skip empty lines and comments
        stripped = line.strip()
        if not stripped or stripped.startswith('#'):
            continue

        # Check for basic indentation issues
        if line.startswith(' ') and not line.startswith('  '):
            # Single space indentation is usually wrong in YAML
            if ':' in line or '-' in line:
                print(f'[FAIL] Potential indentation issue in {file_path}:{line_num}: {line.strip()}')
                return False

        # Check for unmatched quotes
        if line.count('"') % 2 != 0 or line.count("'") % 2 != 0:
            print(f'[FAIL] Unmatched quotes in {file_path}:{line_num}: {line.strip()}')
            return False

        # Check for invalid characters after colon
        if ':' in line and not line.strip().endswith(':'):
            parts = line.split(':', 1)
            if len(parts) == 2:
                value_part = parts[1].strip()
                # Check if value starts with invalid characters
                if value_part and value_part[0] in ['[', '{'] and not (value_part.endswith(']') or value_part.endswith('}')):
                    # This might be a multiline structure, which is okay
                    pass

        # Check for tabs (YAML doesn't allow tabs for indentation)
        if '\t' in line:
            print(f'[FAIL] Tab character found in {file_path}:{line_num} (YAML uses spaces only)')
            return False

    return True


def check_template_processing_issues(content, file_path):
    """Check for common template processing issues"""
    issues = []

    # Check for unprocessed template variables
    unprocessed_vars = re.findall(r'\{\{[^}]+\}\}', content)
    if unprocessed_vars:
        issues.append(f"Unprocessed template variables found: {', '.join(set(unprocessed_vars))}")

    # Check for malformed conditional blocks
    if '{{else}}' in content:
        issues.append("Orphaned {{else}} block found")
    if '{{/eq}}' in content:
        issues.append("Orphaned {{/eq}} block found")
    if '{{/if}}' in content:
        issues.append("Orphaned {{/if}} block found")

    return issues


def validate_kubernetes_manifest(parsed_yaml, file_path):
    """Validate Kubernetes-specific manifest requirements"""
    issues = []

    if not isinstance(parsed_yaml, dict):
        return issues

    # Check for required Kubernetes fields
    if 'apiVersion' not in parsed_yaml:
        issues.append("Missing required field: apiVersion")
    if 'kind' not in parsed_yaml:
        issues.append("Missing required field: kind")
    if 'metadata' not in parsed_yaml:
        issues.append("Missing required field: metadata")
    elif not isinstance(parsed_yaml['metadata'], dict):
        issues.append("metadata field must be an object")
    elif 'name' not in parsed_yaml['metadata']:
        issues.append("Missing required field: metadata.name")

    # Check for ArgoCD-specific requirements
    if 'argocd' in file_path.lower():
        if parsed_yaml.get('kind') == 'Application':
            spec = parsed_yaml.get('spec', {})
            if 'source' not in spec:
                issues.append("ArgoCD Application missing spec.source")
            if 'destination' not in spec:
                issues.append("ArgoCD Application missing spec.destination")

    return issues


def provide_detailed_yaml_error(content, file_path, error_msg):
    """Provide more detailed error information for YAML parsing failures"""
    lines = content.split('\n')

    # Try to extract line number from error message
    line_match = re.search(r'line (\d+)', error_msg)
    if line_match:
        error_line = int(line_match.group(1))
        print(f'[INFO] Error context around line {error_line}:')
        start_line = max(1, error_line - 2)
        end_line = min(len(lines), error_line + 2)

        for i in range(start_line - 1, end_line):
            marker = '>>> ' if i + 1 == error_line else '    '
            print(f'{marker}{i + 1:3d}: {lines[i]}')


def collect_yaml_files(paths):
    files = []
    for path in paths:
        if os.path.isdir(path):
            files.extend(glob.glob(os.path.join(path, '**', '*.yaml'), recursive=True))
        elif os.path.isfile(path) and path.endswith('.yaml'):
            files.append(path)
    return files

def main():
    if len(sys.argv) < 2:
        print('Usage: validate-yaml.py <file-or-directory> [more ...]')
        sys.exit(1)
    files = collect_yaml_files(sys.argv[1:])
    if not files:
        print('No YAML files found to validate.')
        sys.exit(1)
    all_valid = True
    for file_path in files:
        if not validate_yaml_file(file_path):
            all_valid = False
    if not all_valid:
        print('[FAIL] Some YAML files have syntax errors')
        sys.exit(1)
    else:
        print('[PASS] All YAML files are valid')
        sys.exit(0)

if __name__ == '__main__':
    main() 