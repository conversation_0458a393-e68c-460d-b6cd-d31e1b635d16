#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Test cross-cluster ArgoCD deployment

.DESCRIPTION
    This script tests the cross-cluster ArgoCD setup by deploying a test application
    to the target cluster and verifying the deployment.

.PARAMETER Environment
    Environment to test (dev, staging, production)

.PARAMETER ProjectId
    Project ID for the test application

.EXAMPLE
    ./test-cross-cluster-deployment.ps1 -Environment "dev" -ProjectId "test-app"
#>

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("dev", "staging", "production")]
    [string]$Environment,
    
    [Parameter(Mandatory=$true)]
    [string]$ProjectId
)

Write-Host "🧪 Testing cross-cluster ArgoCD deployment..." -ForegroundColor Yellow
Write-Host "📋 Test Configuration:" -ForegroundColor Cyan
Write-Host "   Environment: $Environment" -ForegroundColor White
Write-Host "   Project ID: $ProjectId" -ForegroundColor White

# Determine target cluster based on environment
$clusterInfo = switch ($Environment) {
    "dev" { 
        @{
            server = "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com"
            name = "doks-target-cluster"
            id = "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        }
    }
    "staging" { 
        @{
            server = "https://6be4e15d-52f9-431d-84ec-ec8cad0dff2d.k8s.ondigitalocean.com"
            name = "doks-target-cluster"
            id = "6be4e15d-52f9-431d-84ec-ec8cad0dff2d"
        }
    }
    "production" { 
        @{
            server = "https://kubernetes.default.svc"
            name = "in-cluster"
            id = "158b6a47-3e7e-4dca-af0f-05a6e07115af"
        }
    }
}

Write-Host "   Target Cluster: $($clusterInfo.name) ($($clusterInfo.id))" -ForegroundColor White

# Step 1: Verify ArgoCD connection
Write-Host "`n🔍 Step 1: Verifying ArgoCD connection..." -ForegroundColor Cyan
try {
    $clusters = argocd cluster list 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to connect to ArgoCD. Please ensure you're logged in." -ForegroundColor Red
        Write-Host "   Run: argocd login <your-argocd-server>" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "✅ ArgoCD connection verified" -ForegroundColor Green
} catch {
    Write-Host "❌ ArgoCD CLI error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Verify target cluster is registered
Write-Host "`n🔍 Step 2: Verifying target cluster registration..." -ForegroundColor Cyan
$clusterFound = $false
$clusterList = argocd cluster list --output json | ConvertFrom-Json
foreach ($cluster in $clusterList) {
    if ($cluster.server -eq $clusterInfo.server) {
        $clusterFound = $true
        Write-Host "✅ Target cluster found: $($cluster.name)" -ForegroundColor Green
        Write-Host "   Server: $($cluster.server)" -ForegroundColor White
        Write-Host "   Status: $($cluster.connectionState.status)" -ForegroundColor White
        break
    }
}

if (-not $clusterFound) {
    Write-Host "❌ Target cluster not found in ArgoCD" -ForegroundColor Red
    Write-Host "   Please run: .\scripts\setup-external-cluster.ps1" -ForegroundColor Yellow
    exit 1
}

# Step 3: Generate test manifests
Write-Host "`n🔍 Step 3: Generating test manifests..." -ForegroundColor Cyan
$testParams = @{
    Environment = $Environment
    ProjectId = $ProjectId
    AppName = "Test Application"
    ApplicationType = "springboot-backend"
    DockerImage = "nginx"
    DockerTag = "latest"
    ContainerPort = 80
}

try {
    & ".\scripts\generate-manifests-cicd.ps1" @testParams
    Write-Host "✅ Test manifests generated successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to generate manifests: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Apply ArgoCD manifests
Write-Host "`n🔍 Step 4: Applying ArgoCD manifests..." -ForegroundColor Cyan
$projectFile = "$ProjectId\argocd\project.yaml"
$applicationFile = "$ProjectId\argocd\application.yaml"

if (-not (Test-Path $projectFile) -or -not (Test-Path $applicationFile)) {
    Write-Host "❌ Generated manifest files not found" -ForegroundColor Red
    exit 1
}

try {
    kubectl apply -f $projectFile
    kubectl apply -f $applicationFile
    Write-Host "✅ ArgoCD manifests applied successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to apply manifests: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 5: Wait for application to be created
Write-Host "`n🔍 Step 5: Waiting for ArgoCD application..." -ForegroundColor Cyan
$timeout = 60
$elapsed = 0
$appFound = $false

while ($elapsed -lt $timeout -and -not $appFound) {
    try {
        $app = argocd app get $ProjectId --output json 2>$null | ConvertFrom-Json
        if ($app) {
            $appFound = $true
            Write-Host "✅ ArgoCD application created successfully" -ForegroundColor Green
            Write-Host "   Name: $($app.metadata.name)" -ForegroundColor White
            Write-Host "   Status: $($app.status.health.status)" -ForegroundColor White
            Write-Host "   Sync: $($app.status.sync.status)" -ForegroundColor White
        }
    } catch {
        # Application not ready yet
    }
    
    if (-not $appFound) {
        Start-Sleep 5
        $elapsed += 5
        Write-Host "   Waiting... ($elapsed/$timeout seconds)" -ForegroundColor Yellow
    }
}

if (-not $appFound) {
    Write-Host "❌ ArgoCD application not created within timeout" -ForegroundColor Red
    exit 1
}

# Step 6: Trigger sync
Write-Host "`n🔍 Step 6: Triggering application sync..." -ForegroundColor Cyan
try {
    argocd app sync $ProjectId
    Write-Host "✅ Application sync triggered" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Sync trigger failed, but application may sync automatically" -ForegroundColor Yellow
}

# Step 7: Final verification
Write-Host "`n🔍 Step 7: Final verification..." -ForegroundColor Cyan
Write-Host "📋 Test Results:" -ForegroundColor Green
Write-Host "   ✅ ArgoCD connection verified" -ForegroundColor White
Write-Host "   ✅ Target cluster registered" -ForegroundColor White
Write-Host "   ✅ Manifests generated" -ForegroundColor White
Write-Host "   ✅ ArgoCD application created" -ForegroundColor White
Write-Host "   ✅ Deployment to $($clusterInfo.name) initiated" -ForegroundColor White

Write-Host "`n🎉 Cross-cluster deployment test completed!" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Monitor application status: argocd app get $ProjectId" -ForegroundColor White
Write-Host "   2. Check application logs: argocd app logs $ProjectId" -ForegroundColor White
Write-Host "   3. View in ArgoCD UI for detailed status" -ForegroundColor White
Write-Host "   4. Clean up test: argocd app delete $ProjectId" -ForegroundColor White
