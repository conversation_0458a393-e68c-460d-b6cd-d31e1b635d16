param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("spring", "django", "nest")]
    [string]$Backend
)

$NAMESPACE = "ai-react-frontend-dev"

switch ($Backend) {
    "spring" {
        $URL = "http://64.225.85.162:8080"
        $SERVICE = "ai-spring-backend-service"
        $NS = "ai-spring-backend-dev"
        $HEALTH_ENDPOINT = "/actuator/health"
    }
    "django" {
        $URL = "http://*************:8000"
        $SERVICE = "ai-django-backend-service"
        $NS = "ai-django-backend-dev"
        $HEALTH_ENDPOINT = "/health"
    }
    "nest" {
        $URL = "http://**************:3000"
        $SERVICE = "ai-nest-backend-service"
        $NS = "ai-nest-backend-dev"
        $HEALTH_ENDPOINT = "/health"
    }
}

Write-Host "🔄 Switching to $Backend backend..." -ForegroundColor Yellow

# Get current timestamp
$timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")

# Get current config and update it
$currentConfig = kubectl get configmap ai-react-frontend-runtime-config -n $NAMESPACE -o jsonpath='{.data.runtime-config\.json}'
$configObj = $currentConfig | ConvertFrom-Json

# Update the relevant fields
$configObj.currentBackend = $Backend
$configObj.backendUrl = $URL
$configObj.serviceName = $SERVICE
$configObj.namespace = $NS
$configObj.lastUpdated = $timestamp

# Convert back to JSON and update ConfigMap
$updatedConfig = $configObj | ConvertTo-Json -Depth 10 -Compress
$patchData = @{
    data = @{
        "runtime-config.json" = $updatedConfig
    }
} | ConvertTo-Json -Depth 3

kubectl patch configmap ai-react-frontend-runtime-config -n $NAMESPACE --type merge -p $patchData

# Restart frontend deployment
kubectl rollout restart deployment ai-react-frontend -n $NAMESPACE

# Wait for rollout to complete
Write-Host "⏳ Waiting for deployment to complete..." -ForegroundColor Yellow
kubectl rollout status deployment ai-react-frontend -n $NAMESPACE --timeout=300s

# Test backend health
Write-Host "🔍 Testing backend health..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$URL$HEALTH_ENDPOINT" -Method GET -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ $Backend backend is healthy ($URL)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  $Backend backend may not be responding ($URL)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  $Backend backend may not be responding ($URL)" -ForegroundColor Yellow
}

# Show current config
Write-Host "📋 Current configuration:" -ForegroundColor Cyan
$currentConfigDisplay = kubectl get configmap ai-react-frontend-runtime-config -n $NAMESPACE -o jsonpath='{.data.runtime-config\.json}'
$configDisplayObj = $currentConfigDisplay | ConvertFrom-Json
Write-Host "Current Backend: $($configDisplayObj.currentBackend)" -ForegroundColor White
Write-Host "Backend URL: $($configDisplayObj.backendUrl)" -ForegroundColor White
Write-Host "Service Name: $($configDisplayObj.serviceName)" -ForegroundColor White
Write-Host "Namespace: $($configDisplayObj.namespace)" -ForegroundColor White
Write-Host "Last Updated: $($configDisplayObj.lastUpdated)" -ForegroundColor White

Write-Host "🎉 Successfully switched to $Backend backend!" -ForegroundColor Green
