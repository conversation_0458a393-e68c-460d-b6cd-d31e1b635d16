name: 🚀 Generate Application Manifests

on:
  issues:
    types: [opened, edited]

jobs:
  check-trigger:
    runs-on: [self-hosted, Linux]
    outputs:
      should-run: ${{ steps.check.outputs.should-run }}
    steps:
      - name: 🔍 Check Trigger Conditions
        id: check
        env:
          ISSUE_TITLE: ${{ github.event.issue.title }}
          ISSUE_BODY: ${{ github.event.issue.body }}
          ISSUE_LABELS: ${{ toJson(github.event.issue.labels) }}
          HAS_LABEL: ${{ contains(github.event.issue.labels.*.name, 'app-deployment') }}
        run: |
          echo "=== DEBUGGING TRIGGER CONDITIONS ==="
          echo "Issue title: '$ISSUE_TITLE'"
          echo "Issue labels: $ISSUE_LABELS"
          echo "Has app-deployment label: $HAS_LABEL"
          echo "Issue body length: ${#ISSUE_BODY}"
          echo "Issue body preview: ${ISSUE_BODY:0:200}..."
          echo "=================================="

          # Initialize should_run as false
          should_run=false

          # Check condition 1: Title contains [DEPLOY]
          if [[ "$ISSUE_TITLE" == *"[DEPLOY]"* ]]; then
            echo "✅ Condition 1 met: Title contains [DEPLOY]"
            should_run=true
          else
            echo "❌ Condition 1 failed: Title does not contain [DEPLOY]"
          fi

          # Check condition 2: Has app-deployment label
          if [[ "$HAS_LABEL" == "true" ]]; then
            echo "✅ Condition 2 met: Has app-deployment label"
            should_run=true
          else
            echo "❌ Condition 2 failed: Missing app-deployment label"
          fi

          # Check condition 3: Body contains deployment request marker
          if [[ "$ISSUE_BODY" == *"Application Deployment Request"* ]]; then
            echo "✅ Condition 3 met: Body contains deployment request marker"
            should_run=true
          else
            echo "❌ Condition 3 failed: Body missing deployment request marker"
          fi

          # Set output
          if [[ "$should_run" == "true" ]]; then
            echo "should-run=true" >> $GITHUB_OUTPUT
            echo "🎉 RESULT: Workflow will proceed with deployment"
          else
            echo "should-run=false" >> $GITHUB_OUTPUT
            echo "🚫 RESULT: Workflow will be skipped"
          fi

  validate-and-generate:
    needs: check-trigger
    if: needs.check-trigger.outputs.should-run == 'true'
    runs-on: [self-hosted, Linux]

    permissions:
      contents: write
      issues: write
      pull-requests: write

    steps:
      - name: 🏷️ Add Missing Label
        if: ${{ !contains(github.event.issue.labels.*.name, 'app-deployment') }}
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ['app-deployment']
            });
            console.log('Added app-deployment label to issue');

      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: 🔍 Verify DOKS Environment
        shell: bash
        run: |
          echo "🔍 Verifying DOKS and ARC runner environment..."

          # Check if we're running on the expected ARC runner
          echo "Runner OS: ${{ runner.os }}"
          echo "Runner architecture: ${{ runner.arch }}"

          # Check kubectl availability (will be installed later if missing)
          if command -v kubectl >/dev/null 2>&1; then
            echo "✅ kubectl is available"
            kubectl version --client --short
          else
            echo "⚠️ kubectl not found - will be installed during auto-deployment setup"
          fi

          # Check cluster connectivity (if kubectl is available)
          if command -v kubectl >/dev/null 2>&1; then
            if kubectl cluster-info --request-timeout=10s >/dev/null 2>&1; then
              echo "✅ Kubernetes cluster is accessible"
              kubectl cluster-info
            else
              echo "⚠️ Cannot connect to Kubernetes cluster - will be verified during auto-deployment"
            fi
          else
            echo "ℹ️ Skipping cluster connectivity check - kubectl not available"
          fi

          # Check if PowerShell is available
          if command -v pwsh >/dev/null 2>&1; then
            echo "✅ PowerShell is available"
            pwsh --version
          else
            echo "⚠️ PowerShell not found - will be installed automatically"
          fi

          # Check ArgoCD CRDs (if kubectl is available)
          if command -v kubectl >/dev/null 2>&1 && kubectl cluster-info --request-timeout=5s >/dev/null 2>&1; then
            if kubectl get crd applications.argoproj.io >/dev/null 2>&1; then
              echo "✅ ArgoCD CRDs are available"
            else
              echo "⚠️ ArgoCD CRDs not found - may need to install ArgoCD"
            fi
          else
            echo "ℹ️ Skipping ArgoCD check - cluster not accessible"
          fi
      
      - name: 🔧 Setup PowerShell
        id: setup-powershell
        run: |
          echo "🔍 Checking PowerShell availability..."

          # Check if pwsh (PowerShell Core) is available
          if command -v pwsh >/dev/null 2>&1; then
            echo "✅ PowerShell Core (pwsh) is available"
            pwsh --version
            echo "powershell-command=pwsh" >> $GITHUB_OUTPUT
            echo "powershell-available=true" >> $GITHUB_OUTPUT
          elif command -v powershell >/dev/null 2>&1; then
            echo "✅ Windows PowerShell is available"
            powershell -Command "Write-Host 'PowerShell Version:'; $PSVersionTable.PSVersion"
            echo "powershell-command=powershell" >> $GITHUB_OUTPUT
            echo "powershell-available=true" >> $GITHUB_OUTPUT
          else
            echo "⚠️ No PowerShell found, attempting to install PowerShell Core..."

            # Try to install PowerShell Core
            if command -v apt-get >/dev/null 2>&1; then
              # Ubuntu/Debian
              echo "📦 Installing PowerShell Core on Ubuntu/Debian..."
              wget -q https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O /tmp/packages-microsoft-prod.deb
              sudo dpkg -i /tmp/packages-microsoft-prod.deb
              sudo apt-get update
              sudo apt-get install -y powershell
            elif command -v yum >/dev/null 2>&1; then
              # CentOS/RHEL/Fedora
              echo "📦 Installing PowerShell Core on CentOS/RHEL/Fedora..."
              sudo yum install -y https://packages.microsoft.com/config/rhel/8/packages-microsoft-prod.rpm
              sudo yum install -y powershell
            else
              echo "❌ Unsupported system for PowerShell installation"
              echo "powershell-available=false" >> $GITHUB_OUTPUT
              exit 1
            fi

            # Verify installation
            if command -v pwsh >/dev/null 2>&1; then
              echo "✅ PowerShell Core installed successfully"
              pwsh --version
              echo "powershell-command=pwsh" >> $GITHUB_OUTPUT
              echo "powershell-available=true" >> $GITHUB_OUTPUT
            else
              echo "❌ PowerShell installation failed"
              echo "powershell-available=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          fi

      - name: 📋 Extract Issue Data
        id: extract
        if: steps.setup-powershell.outputs.powershell-available == 'true'
        shell: bash
        run: |
          # Save issue body to file for processing
          cat > issue-body.txt << 'EOF'
          ${{ github.event.issue.body }}
          EOF

          # Extract key values using PowerShell
          if [ "${{ steps.setup-powershell.outputs.powershell-command }}" = "pwsh" ]; then
            pwsh -Command "
              \$issueBody = Get-Content 'issue-body.txt' -Raw
              function Get-IssueValue {
                  param([string]\$Body, [string]\$FieldName)
                  \$pattern = \"### \$FieldName\\s*\\n\\s*(.+?)(?=\\n###|\\n\\n|\\Z)\"
                  \$match = [regex]::Match(\$Body, \$pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
                  if (\$match.Success) { return \$match.Groups[1].Value.Trim() }
                  return \"\"
              }
              \$projectId = Get-IssueValue -Body \$issueBody -FieldName 'Project Identifier'
              \$appName = Get-IssueValue -Body \$issueBody -FieldName 'Application Name'
              \$environment = Get-IssueValue -Body \$issueBody -FieldName 'Environment'
              if (-not \$projectId -or \$projectId -eq \"\") { Write-Host \"❌ Project Identifier is required\"; exit 1 }
              if (\$projectId -notmatch '^[a-z0-9-]+$') { Write-Host \"❌ Project Identifier must be lowercase alphanumeric with hyphens only\"; exit 1 }
              echo \"project-id=\$projectId\" >> \$env:GITHUB_OUTPUT
              echo \"app-name=\$appName\" >> \$env:GITHUB_OUTPUT
              echo \"environment=\$environment\" >> \$env:GITHUB_OUTPUT
              Write-Host \"✅ Extracted project ID: \$projectId\"
              Write-Host \"✅ Extracted app name: \$appName\"
              Write-Host \"✅ Extracted environment: \$environment\"
            "
          else
            powershell -Command "
              \$issueBody = Get-Content 'issue-body.txt' -Raw
              function Get-IssueValue {
                  param([string]\$Body, [string]\$FieldName)
                  \$pattern = \"### \$FieldName\\s*\\n\\s*(.+?)(?=\\n###|\\n\\n|\\Z)\"
                  \$match = [regex]::Match(\$Body, \$pattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
                  if (\$match.Success) { return \$match.Groups[1].Value.Trim() }
                  return \"\"
              }
              \$projectId = Get-IssueValue -Body \$issueBody -FieldName 'Project Identifier'
              \$appName = Get-IssueValue -Body \$issueBody -FieldName 'Application Name'
              \$environment = Get-IssueValue -Body \$issueBody -FieldName 'Environment'
              if (-not \$projectId -or \$projectId -eq \"\") { Write-Host \"❌ Project Identifier is required\"; exit 1 }
              if (\$projectId -notmatch '^[a-z0-9-]+$') { Write-Host \"❌ Project Identifier must be lowercase alphanumeric with hyphens only\"; exit 1 }
              echo \"project-id=\$projectId\" >> \$env:GITHUB_OUTPUT
              echo \"app-name=\$appName\" >> \$env:GITHUB_OUTPUT
              echo \"environment=\$environment\" >> \$env:GITHUB_OUTPUT
              Write-Host \"✅ Extracted project ID: \$projectId\"
              Write-Host \"✅ Extracted app name: \$appName\"
              Write-Host \"✅ Extracted environment: \$environment\"
            "
          fi

      - name: 🔍 Validate Templates
        shell: bash
        run: |
          echo "🔍 Validating template files..."

          template_files=(
              "templates/argocd/application.yaml"
              "templates/argocd/project.yaml"
              "templates/k8s/namespace.yaml"
              "templates/k8s/configmap.yaml"
              "templates/k8s/secret.yaml"
              "templates/k8s/deployment.yaml"
              "templates/k8s/service.yaml"
          )

          missing_templates=()
          for template in "${template_files[@]}"; do
              if [ ! -f "$template" ]; then
                  missing_templates+=("$template")
                  echo "❌ Missing template: $template"
              else
                  echo "✅ Found template: $template"
              fi
          done

          if [ ${#missing_templates[@]} -gt 0 ]; then
              echo "❌ Missing ${#missing_templates[@]} required template files"
              exit 1
          fi

          echo "✅ All required templates found"

      - name: 🔍 Check for Existing Project
        id: check-existing
        shell: bash
        run: |
          PROJECT_ID="${{ steps.extract.outputs.project-id }}"
          
          if [ -d "$PROJECT_ID" ]; then
            echo "exists=true" >> $GITHUB_OUTPUT
            echo "⚠️ Project directory already exists: $PROJECT_ID"
          else
            echo "exists=false" >> $GITHUB_OUTPUT
            echo "✅ Project directory is available: $PROJECT_ID"
          fi
      
      - name: 🚨 Comment on Existing Project
        if: steps.check-existing.outputs.exists == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const projectId = '${{ steps.extract.outputs.project-id }}';
            const comment = `## ⚠️ Project Already Exists
            
            A project with the identifier \`${projectId}\` already exists in this repository.
            
            ### Options:
            1. **Choose a different Project Identifier** - Edit this issue and use a unique identifier
            2. **Update existing project** - Close this issue and create a new one with label \`app-update\`
            3. **Force overwrite** - Add the label \`force-overwrite\` to this issue
            
            ### Existing Project Structure:
            - \`${projectId}/argocd/\` - ArgoCD application manifests
            - \`${projectId}/k8s/\` - Kubernetes manifests
            
            Please resolve this conflict before proceeding.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
      
      - name: 🛑 Stop if Project Exists (without force)
        if: steps.check-existing.outputs.exists == 'true' && !contains(github.event.issue.labels.*.name, 'force-overwrite')
        run: |
          echo "❌ Project already exists and force-overwrite label not present"
          exit 1
      
      - name: 📝 Generate Manifests
        id: generate
        if: steps.setup-powershell.outputs.powershell-available == 'true'
        shell: bash
        run: |
          echo "🚀 Starting manifest generation..."

          # Verify script exists
          if [ ! -f "scripts/generate-manifests.ps1" ]; then
            echo "❌ Script not found: scripts/generate-manifests.ps1"
            exit 1
          fi

          # Verify issue body file exists
          if [ ! -f "issue-body.txt" ]; then
            echo "❌ Issue body file not found: issue-body.txt"
            exit 1
          fi

          echo "📋 Reading issue body..."
          issue_body_content=$(cat issue-body.txt)
          echo "📋 Issue body length: ${#issue_body_content} characters"

          # Run the PowerShell script
          echo "🔧 Executing manifest generation script..."
          if [ "${{ steps.setup-powershell.outputs.powershell-command }}" = "pwsh" ]; then
            pwsh -File ./scripts/generate-manifests.ps1 -IssueBody "$issue_body_content" -OutputDir "."
            status=$?
          else
            powershell -File ./scripts/generate-manifests.ps1 -IssueBody "$issue_body_content" -OutputDir "."
            status=$?
          fi

          if [ $status -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "✅ Manifest generation completed successfully"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Manifest generation failed with exit code: $status"
            exit 1
          fi
      
      - name: 📊 Validate Generated Files
        id: validate
        shell: bash
        run: |
          PROJECT_ID="${{ steps.extract.outputs.project-id }}"

          echo "🔍 Validating generated files..."

          # Check required directories
          if [ ! -d "$PROJECT_ID/argocd" ]; then
            echo "❌ ArgoCD directory not created"
            exit 1
          fi

          if [ ! -d "$PROJECT_ID/k8s" ]; then
            echo "❌ Kubernetes directory not created"
            exit 1
          fi

          # Check required files
          required_files=(
            "$PROJECT_ID/argocd/application.yaml"
            "$PROJECT_ID/argocd/project.yaml"
            "$PROJECT_ID/k8s/namespace.yaml"
            "$PROJECT_ID/k8s/configmap.yaml"
            "$PROJECT_ID/k8s/secret.yaml"
            "$PROJECT_ID/k8s/deployment.yaml"
            "$PROJECT_ID/k8s/service.yaml"
          )

          for file in "${required_files[@]}"; do
            if [ ! -f "$file" ]; then
              echo "❌ Required file not found: $file"
              exit 1
            fi
            echo "✅ Found: $file"

            # Check if file is not empty
            if [ ! -s "$file" ]; then
              echo "❌ File is empty: $file"
              exit 1
            fi

            # Basic YAML syntax check using our validation script
            if command -v python3 >/dev/null 2>&1; then
              # Install PyYAML if not available
              pip3 install PyYAML --user --quiet 2>/dev/null || true

              if ! python3 scripts/validate-yaml.py "$file" >/dev/null 2>&1; then
                echo "❌ Invalid YAML syntax in: $file"
                echo "File contents:"
                cat "$file"
                exit 1
              fi
            fi
          done

          # Validate YAML syntax with yamllint if available
          if command -v yamllint >/dev/null 2>&1; then
            echo "🔍 Validating YAML syntax with yamllint..."
            find "$PROJECT_ID" -name "*.yaml" -exec yamllint {} \; || true
          else
            echo "ℹ️ yamllint not available, skipping advanced YAML validation"
          fi

          echo "✅ File validation completed"
      
      - name: 📁 List Generated Files
        shell: bash
        run: |
          PROJECT_ID="${{ steps.extract.outputs.project-id }}"
          echo "📁 Generated project structure:"
          tree "$PROJECT_ID" || find "$PROJECT_ID" -type f | sort
      
      - name: 🔧 Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
      
      - name: 💾 Commit Generated Files
        id: commit
        run: |
          PROJECT_ID="${{ steps.extract.outputs.project-id }}"
          APP_NAME="${{ steps.extract.outputs.app-name }}"
          ENVIRONMENT="${{ steps.extract.outputs.environment }}"
          
          # Add generated files
          git add "$PROJECT_ID/"
          
          # Check if there are changes to commit
          if git diff --staged --quiet; then
            echo "No changes to commit"
            echo "committed=false" >> $GITHUB_OUTPUT
          else
            # Commit changes
            git commit -m "🚀 Add $APP_NAME ($PROJECT_ID) deployment manifests for $ENVIRONMENT
            
            Generated by GitOps automation from issue #${{ github.event.issue.number }}
            
            - ArgoCD Application and Project manifests
            - Complete Kubernetes deployment manifests
            - Environment: $ENVIRONMENT
            - Application Type: $(grep -o 'app-type: [^"]*' $PROJECT_ID/k8s/namespace.yaml | cut -d' ' -f2 || echo 'unknown')
            
            Co-authored-by: ${{ github.event.issue.user.login }} <${{ github.event.issue.user.login }}@users.noreply.github.com>"
            
            echo "committed=true" >> $GITHUB_OUTPUT
            echo "✅ Changes committed successfully"
          fi
      
      - name: 🚀 Push Changes
        if: steps.commit.outputs.committed == 'true'
        run: |
          git push origin main
          echo "✅ Changes pushed to main branch"

      - name: 🔧 Setup kubectl for ARC Runner
        id: setup-kubectl
        if: steps.generate.outputs.success == 'true'
        continue-on-error: true
        run: |
          echo "🔧 Setting up kubectl for ARC runner..."

          # Check if auto-deployment is enabled
          AUTO_DEPLOY_ENABLED="${{ vars.ENABLE_AUTO_DEPLOY }}"
          if [ "$AUTO_DEPLOY_ENABLED" != "true" ]; then
            echo "auto-deploy-enabled=false" >> $GITHUB_OUTPUT
            echo "ℹ️ Auto-deployment is disabled (ENABLE_AUTO_DEPLOY != true)"
            exit 0
          fi

          echo "auto-deploy-enabled=true" >> $GITHUB_OUTPUT
          echo "✅ Auto-deployment is enabled"

          # Install kubectl if not available
          if ! command -v kubectl >/dev/null 2>&1; then
            echo "📦 Installing kubectl..."
            curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
            echo "✅ kubectl installed successfully"
          else
            echo "✅ kubectl is already available"
          fi

          echo "kubectl-available=true" >> $GITHUB_OUTPUT

          # Test cluster connectivity (should already be configured in ARC runner)
          echo "🔍 Testing cluster connectivity..."
          if timeout 15s kubectl cluster-info >/dev/null 2>&1; then
            echo "cluster-accessible=true" >> $GITHUB_OUTPUT
            echo "✅ Kubernetes cluster is accessible"

            # Test ArgoCD access
            if kubectl get crd applications.argoproj.io >/dev/null 2>&1; then
              echo "argocd-available=true" >> $GITHUB_OUTPUT
              echo "✅ ArgoCD CRDs are available"

              # Test ArgoCD namespace
              if kubectl get namespace argocd >/dev/null 2>&1; then
                echo "argocd-namespace-exists=true" >> $GITHUB_OUTPUT
                echo "✅ ArgoCD namespace exists"
              else
                echo "argocd-namespace-exists=false" >> $GITHUB_OUTPUT
                echo "⚠️ ArgoCD namespace not found"
              fi
            else
              echo "argocd-available=false" >> $GITHUB_OUTPUT
              echo "⚠️ ArgoCD CRDs not found"
            fi
          else
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            echo "❌ Kubernetes cluster not accessible from ARC runner"
          fi

      - name: 🚀 Auto-Deploy to ArgoCD
        id: auto-deploy
        if: steps.setup-kubectl.outputs.auto-deploy-enabled == 'true' && steps.setup-kubectl.outputs.kubectl-available == 'true' && steps.setup-kubectl.outputs.cluster-accessible == 'true' && steps.setup-kubectl.outputs.argocd-available == 'true'
        continue-on-error: true
        run: |
          PROJECT_ID="${{ steps.extract.outputs.project-id }}"
          APP_NAME="${{ steps.extract.outputs.app-name }}"
          ENVIRONMENT="${{ steps.extract.outputs.environment }}"

          echo "🚀 Starting automated ArgoCD deployment for: $PROJECT_ID"
          echo "📋 Application: $APP_NAME"
          echo "🌍 Environment: $ENVIRONMENT"

          # Initialize deployment status
          DEPLOYMENT_SUCCESS=false
          DEPLOYMENT_ERROR=""

          # Function to wait for application sync
          wait_for_sync() {
            local app_name=$1
            local timeout_seconds=${2:-180}
            local start_time=$(date +%s)
            local end_time=$((start_time + timeout_seconds))

            echo "⏳ Waiting for ArgoCD application sync (timeout: ${timeout_seconds}s)..."

            while [ $(date +%s) -lt $end_time ]; do
              if kubectl get application $app_name -n argocd >/dev/null 2>&1; then
                local sync_status=$(kubectl get application $app_name -n argocd -o jsonpath='{.status.sync.status}' 2>/dev/null || echo "Unknown")
                local health_status=$(kubectl get application $app_name -n argocd -o jsonpath='{.status.health.status}' 2>/dev/null || echo "Unknown")

                echo "🔄 Sync Status: $sync_status, Health: $health_status"

                if [ "$sync_status" = "Synced" ] && [ "$health_status" = "Healthy" ]; then
                  echo "✅ Application is synced and healthy!"
                  return 0
                elif [ "$sync_status" = "OutOfSync" ] || [ "$health_status" = "Degraded" ]; then
                  echo "⚠️ Application sync issues detected, continuing to wait..."
                fi
              else
                echo "⚠️ Application not found yet, waiting..."
              fi

              sleep 10
            done

            echo "⏰ Timeout waiting for application sync"
            return 1
          }

          # Step 1: Validate manifest files exist
          echo "📋 Validating generated manifest files..."

          PROJECT_FILE="${PROJECT_ID}/argocd/project.yaml"
          APPLICATION_FILE="${PROJECT_ID}/argocd/application.yaml"

          if [ ! -f "$PROJECT_FILE" ]; then
            DEPLOYMENT_ERROR="ArgoCD project manifest not found: $PROJECT_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          if [ ! -f "$APPLICATION_FILE" ]; then
            DEPLOYMENT_ERROR="ArgoCD application manifest not found: $APPLICATION_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "✅ Manifest files validated successfully"

          # Step 2: Apply ArgoCD Project
          echo "📋 Applying ArgoCD Project..."
          if kubectl apply -f "$PROJECT_FILE"; then
            echo "✅ ArgoCD Project applied successfully"
          else
            DEPLOYMENT_ERROR="Failed to apply ArgoCD Project: $PROJECT_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Step 3: Apply ArgoCD Application
          echo "🎯 Applying ArgoCD Application..."
          if kubectl apply -f "$APPLICATION_FILE"; then
            echo "✅ ArgoCD Application applied successfully"
          else
            DEPLOYMENT_ERROR="Failed to apply ArgoCD Application: $APPLICATION_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Step 4: Verify application creation
          echo "🔍 Verifying ArgoCD application creation..."
          sleep 5  # Give ArgoCD a moment to process

          if kubectl get application $PROJECT_ID -n argocd >/dev/null 2>&1; then
            echo "✅ ArgoCD application created successfully"
          else
            DEPLOYMENT_ERROR="ArgoCD application was not created or is not accessible"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Step 5: Wait for initial sync
          echo "⏳ Waiting for initial ArgoCD sync..."
          if wait_for_sync $PROJECT_ID 180; then
            DEPLOYMENT_SUCCESS=true
            echo "🎉 ArgoCD deployment completed successfully!"
          else
            echo "⚠️ ArgoCD application deployed but sync timeout reached"
            echo "ℹ️ Application may still be syncing in the background"
            DEPLOYMENT_SUCCESS=true  # Consider it successful even if sync is slow
          fi

          # Step 6: Get final application status
          APP_SYNC_STATUS=$(kubectl get application $PROJECT_ID -n argocd -o jsonpath='{.status.sync.status}' 2>/dev/null || echo "Unknown")
          APP_HEALTH_STATUS=$(kubectl get application $PROJECT_ID -n argocd -o jsonpath='{.status.health.status}' 2>/dev/null || echo "Unknown")
          APP_SERVER_VERSION=$(kubectl get application $PROJECT_ID -n argocd -o jsonpath='{.status.sync.revision}' 2>/dev/null || echo "Unknown")

          echo "📊 Final Application Status:"
          echo "   Sync Status: $APP_SYNC_STATUS"
          echo "   Health Status: $APP_HEALTH_STATUS"
          echo "   Revision: $APP_SERVER_VERSION"

          # Set outputs
          if [ "$DEPLOYMENT_SUCCESS" = true ]; then
            echo "deployment-success=true" >> $GITHUB_OUTPUT
            echo "✅ Auto-deployment completed successfully"
          else
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "❌ Auto-deployment failed"
          fi

          echo "app-sync-status=$APP_SYNC_STATUS" >> $GITHUB_OUTPUT
          echo "app-health-status=$APP_HEALTH_STATUS" >> $GITHUB_OUTPUT
          echo "app-revision=$APP_SERVER_VERSION" >> $GITHUB_OUTPUT
          echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT

      - name: 🎉 Success Comment
        if: steps.generate.outputs.success == 'true'
        uses: actions/github-script@v7
        with:
          script: |
            const projectId = '${{ steps.extract.outputs.project-id }}';
            const appName = '${{ steps.extract.outputs.app-name }}';
            const environment = '${{ steps.extract.outputs.environment }}';
            const autoDeployEnabled = '${{ steps.setup-kubectl.outputs.auto-deploy-enabled }}' === 'true';
            const deploymentSuccess = '${{ steps.auto-deploy.outputs.deployment-success }}' === 'true';
            const kubectlAvailable = '${{ steps.setup-kubectl.outputs.kubectl-available }}' === 'true';
            const clusterAccessible = '${{ steps.setup-kubectl.outputs.cluster-accessible }}' === 'true';
            const argoCDAvailable = '${{ steps.setup-kubectl.outputs.argocd-available }}' === 'true';
            const appSyncStatus = '${{ steps.auto-deploy.outputs.app-sync-status }}';
            const appHealthStatus = '${{ steps.auto-deploy.outputs.app-health-status }}';
            const deploymentError = '${{ steps.auto-deploy.outputs.deployment-error }}';

            let deploymentStatus = '';
            let nextSteps = '';

            if (autoDeployEnabled && kubectlAvailable && clusterAccessible && argoCDAvailable) {
              if (deploymentSuccess) {
                deploymentStatus = `
            ### ✅ Automated Deployment Status
            🎉 **ArgoCD application deployed and running successfully!** Your application is now live in the cluster.

            - **Application Name**: \`${projectId}\`
            - **Environment**: \`${environment}\`
            - **Namespace**: \`${projectId}\`
            - **Sync Status**: \`${appSyncStatus}\`
            - **Health Status**: \`${appHealthStatus}\`
            - **Deployed via**: ARC Self-hosted Runner

            #### 🔗 Access ArgoCD Dashboard:
            \`\`\`bash
            kubectl port-forward svc/argocd-server -n argocd 8080:443
            # Then visit: https://*************:8080
            \`\`\``;

                nextSteps = `
            ### 🚀 Your Application is Live!

            #### 1. Monitor Application Status
            \`\`\`bash
            # Check ArgoCD application status
            kubectl get application ${projectId} -n argocd

            # Monitor pods in your namespace
            kubectl get pods -n ${projectId}

            # View application logs
            kubectl logs -n ${projectId} -l app=${projectId} -f
            \`\`\`

            #### 2. Access Your Application
            - **Service**: \`${projectId}-service.${projectId}.svc.cluster.local\`
            - **Namespace**: \`${projectId}\`
            - **ArgoCD App**: [View in ArgoCD Dashboard](https://localhost:8080/applications/${projectId})

            #### 3. Update Configuration (If Needed)
            Your application is deployed with the configuration from the issue. To update:
            \`\`\`bash
            # Edit secrets if needed
            kubectl edit secret ${projectId}-secret -n ${projectId}

            # Update ConfigMaps
            kubectl edit configmap ${projectId}-config -n ${projectId}

            # ArgoCD will automatically sync changes from Git
            \`\`\`

            #### 4. Troubleshooting
            If you notice any issues:
            \`\`\`bash
            # Check application events
            kubectl get events -n ${projectId} --sort-by='.lastTimestamp'

            # Describe the application
            kubectl describe application ${projectId} -n argocd

            # Force sync if needed
            kubectl patch application ${projectId} -n argocd --type merge -p '{"operation":{"sync":{"syncStrategy":{"hook":{"force":true}}}}}'
            \`\`\``;
              } else {
                const errorDetails = deploymentError ? `\n\n**Error Details**: ${deploymentError}` : '';
                deploymentStatus = `
            ### ⚠️ Automated Deployment Status
            ❌ **Automated deployment failed.** The manifests were generated successfully, but ArgoCD deployment encountered an error.${errorDetails}

            The manifests are ready for manual deployment.`;

                nextSteps = `
            ### 🚀 Manual Deployment Steps:

            #### 1. Deploy with ArgoCD
            \`\`\`bash
            # Deploy the ArgoCD application
            kubectl apply -f ${projectId}/argocd/project.yaml
            kubectl apply -f ${projectId}/argocd/application.yaml

            # Verify deployment
            kubectl get application ${projectId} -n argocd
            kubectl get pods -n ${projectId}
            \`\`\`

            #### 2. Monitor Application Status
            \`\`\`bash
            # Check application sync status
            kubectl describe application ${projectId} -n argocd

            # Watch for pods to start
            kubectl get pods -n ${projectId} -w

            # Check application logs
            kubectl logs -n ${projectId} -l app=${projectId}
            \`\`\`

            #### 3. Troubleshooting
            If deployment issues persist:
            \`\`\`bash
            # Check ArgoCD server logs
            kubectl logs -n argocd -l app.kubernetes.io/name=argocd-server

            # Verify ArgoCD project permissions
            kubectl get appproject -n argocd

            # Check for resource conflicts
            kubectl get events -n ${projectId} --sort-by='.lastTimestamp'
            \`\`\`

            #### 4. Update Configuration
            Edit \`${projectId}/k8s/secret.yaml\` and replace placeholder values:
            \`\`\`bash
            echo -n "your-secret-value" | base64
            kubectl apply -f ${projectId}/k8s/secret.yaml
            \`\`\``;
              }
            } else {
              let reason = '';
              let enableInstructions = '';

              if (!autoDeployEnabled) {
                reason = 'Automated deployment is disabled.';
                enableInstructions = `

            **To enable automated deployment:**
            1. Go to repository Settings → Secrets and variables → Actions → Variables
            2. Create a new variable: \`ENABLE_AUTO_DEPLOY\` = \`true\`
            3. Create a new deployment issue to test automated deployment`;
              } else if (!kubectlAvailable) {
                reason = 'kubectl is not available in the ARC runner environment.';
              } else if (!clusterAccessible) {
                reason = 'Kubernetes cluster is not accessible from the ARC runner.';
              } else if (!argoCDAvailable) {
                reason = 'ArgoCD is not installed or accessible in the cluster.';
              }

              deploymentStatus = `
            ### 📋 Deployment Status
            ℹ️ **Manual deployment required.** ${reason}${enableInstructions}`;

              nextSteps = `
            ### 🚀 Manual Deployment Steps:

            #### 1. Review Generated Manifests
            Check the generated files in the [\`${projectId}\`](../../tree/main/${projectId}) directory:
            - \`${projectId}/argocd/project.yaml\` - ArgoCD Project configuration
            - \`${projectId}/argocd/application.yaml\` - ArgoCD Application configuration
            - \`${projectId}/k8s/\` - Complete Kubernetes manifests

            #### 2. Update Configuration (If Needed)
            Edit \`${projectId}/k8s/secret.yaml\` and replace placeholder values with actual base64-encoded secrets:
            \`\`\`bash
            echo -n "your-secret-value" | base64
            \`\`\`

            #### 3. Deploy with ArgoCD
            \`\`\`bash
            # Deploy the ArgoCD application
            kubectl apply -f ${projectId}/argocd/project.yaml
            kubectl apply -f ${projectId}/argocd/application.yaml

            # Monitor deployment
            kubectl get applications -n argocd
            kubectl get pods -n ${projectId}
            \`\`\``;
            }

            const comment = `## 🎉 Deployment Manifests Generated Successfully!

            Your application **${appName}** has been configured for deployment in the **${environment}** environment.

            ### 📁 Generated Files:
            - \`${projectId}/argocd/application.yaml\` - ArgoCD Application manifest
            - \`${projectId}/argocd/project.yaml\` - ArgoCD Project manifest
            - \`${projectId}/k8s/\` - Complete Kubernetes manifests
            ${deploymentStatus}
            ${nextSteps}

            #### 4. Access Your Application
            - **Service**: \`${projectId}-service.${projectId}.svc.cluster.local\`
            - **Namespace**: \`${projectId}\`
            
            ### 📚 Documentation
            - [ArgoCD Documentation](https://argo-cd.readthedocs.io/)
            - [Kubernetes Documentation](https://kubernetes.io/docs/)
            
            ---
            
            **Issue will be automatically closed.** If you need to make changes, create a new deployment issue.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
            
            // Close the issue
            github.rest.issues.update({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'closed'
            });
      
      - name: ❌ Failure Comment
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const comment = `## ❌ Deployment Manifest Generation Failed
            
            There was an error generating the deployment manifests for your application.
            
            ### 🔍 Common Issues:
            1. **Invalid Project Identifier** - Must be lowercase alphanumeric with hyphens only
            2. **Missing Required Fields** - Check that all required fields are filled
            3. **Invalid Resource Values** - Verify CPU/memory formats (e.g., "100m", "256Mi")
            4. **Port Conflicts** - Ensure NodePort is between 30000-32767
            
            ### 🛠️ Next Steps:
            1. Review the [workflow logs](../../actions) for detailed error information
            2. Edit this issue to fix any validation errors
            3. The workflow will automatically retry when you update the issue
            
            ### 📚 Help Resources:
            - [Issue Template Guide](../../blob/main/.github/ISSUE_TEMPLATE/app-deployment.yml)
            - [Kubernetes Resource Documentation](https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/)
            
            Please fix the issues and update this issue to retry the generation.`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
